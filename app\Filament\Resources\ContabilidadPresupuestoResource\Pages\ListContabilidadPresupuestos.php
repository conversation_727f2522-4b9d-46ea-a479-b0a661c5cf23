<?php

namespace App\Filament\Resources\ContabilidadPresupuestoResource\Pages;

use App\Filament\Resources\ContabilidadPresupuestoResource;
use Filament\Actions;
use Filament\Resources\Pages\ListRecords;

class ListContabilidadPresupuestos extends ListRecords
{
    protected static string $resource = ContabilidadPresupuestoResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\CreateAction::make(),
        ];
    }
}
