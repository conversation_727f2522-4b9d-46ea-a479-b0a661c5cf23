<?php
namespace App\Enums;

use Filament\Support\Contracts\HasLabel;
use Filament\Support\Contracts\HasIcon;
use Filament\Support\Contracts\HasColor;

enum Prioridad: string implements HasLabel, HasIcon, HasColor
{
    case Alta = 'Alta';
    case Media = 'Media';
    case Baja = 'Baja';

    public function getColor(): string|array|null
    {
        return match($this) {
            self::Media => 'warning',
            self::Alta => 'danger',
            self::Baja => 'primary',
        };
    }

    public function getLabel(): string
    {
        return $this->name;
    }

    public function getIcon(): ?string
    {
        return match ($this) {
            self::Alta => 'heroicon-o-fire',
            self::Media => 'heroicon-o-exclamation-triangle',
            self::Baja => 'heroicon-o-bars-2',
        };
    }
}


