<div
    id="{{ $record->id }}"
    wire:click="recordClicked('{{ $record->id }}', {{ @json_encode($record) }})"
    class="record transition bg-white dark:bg-gray-700 rounded-lg px-4 py-2 cursor-grab font-medium text-gray-600 dark:text-gray-200"
    @if($record->just_updated)
        x-data
        x-init="
            $el.classList.add('animate-pulse-twice', 'bg-primary-100', 'dark:bg-primary-800')
            $el.classList.remove('bg-white', 'dark:bg-gray-700')
            setTimeout(() => {
                $el.classList.remove('bg-primary-100', 'dark:bg-primary-800')
                $el.classList.add('bg-white', 'dark:bg-gray-700')
            }, 3000)
        "
    @endif
>
    <div class="flex justify-between">
        <div>
            {{ $record->codigo }} {{ $record->nombre }}
        </div>

        <div class="inline-block text-xs text-right text-gray-400">
            {{ $record->fecha_vencimiento?->format('d/m/Y') }}
        </div>
    </div>
    <div class="mt-2 relative">
        <div class="absolute h-3 bg-primary-500 rounded-full" style="width: {{ $record->progreso }}%"></div>
        <div class="h-3 bg-gray-200 rounded-full"></div>
    </div>

    <div class="text-sm text-gray-400 border-l-4 pl-2 mt-2 mb-2">
        {{ $record->proyecto?->codigo_nombre }}
    </div>

    <div class="text-sm text-gray-400 border-l-4 pl-2 mt-2 mb-2">
        {{ $record->proyectoLinea?->codigo }} {{ $record->proyectoLinea?->nombre }}
    </div>

    <div class="text-xs text-gray-400 border-l-4 pl-2 mt-2 mb-2">
        {{ $record->descripcion }}
    </div>

</div>