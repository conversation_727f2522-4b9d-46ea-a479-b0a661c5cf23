<?php
namespace App\Enums;

use Filament\Support\Contracts\HasColor;

enum AlbaranEstado: string implements HasColor
{
    case Pendiente = 'Pendiente';
    case Cancelado = 'Cancelado';
    case En_Proceso = 'En Proceso';
    case Cobrado = 'Cobrado';
    case Terminado = 'Terminado';


    public function getColor(): string|array|null
    {
        return match($this) {
            self::Pendiente => 'danger',
            self::Cancelado => 'danger',
            self::En_Proceso => 'primary',
            self::Cobrado => 'success',
            self::Terminado => 'success',
        };
    }
}


