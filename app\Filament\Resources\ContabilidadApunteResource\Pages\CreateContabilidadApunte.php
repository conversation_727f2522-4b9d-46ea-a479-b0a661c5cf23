<?php

namespace App\Filament\Resources\ContabilidadApunteResource\Pages;

use App\Filament\Resources\ContabilidadApunteResource;
use App\Models\ContabilidadApunte;
use Filament\Actions;
use Filament\Resources\Pages\CreateRecord;

class CreateContabilidadApunte extends CreateRecord
{
    protected static string $resource = ContabilidadApunteResource::class;

    protected function mutateFormDataBeforeCreate(array $data): array
    {
        return ContabilidadApunte::onCreate($data);
    }

    protected function getRedirectUrl(): string
    {
        return $this->getResource()::getUrl('index');
    }
}
