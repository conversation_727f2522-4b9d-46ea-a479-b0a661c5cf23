<?php

namespace App\Filament\Resources\ContabilidadAsignacionCuentaResResource\Pages;

use App\Filament\Resources\ContabilidadAsignacionCuentaResResource;
use Filament\Actions;
use Filament\Resources\Pages\ListRecords;

class ListContabilidadAsignacionCuentaRes extends ListRecords
{
    protected static string $resource = ContabilidadAsignacionCuentaResResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\CreateAction::make(),
        ];
    }
}
