<?php

namespace App\Filament\Resources\ContabilidadApunteResource\Pages;

use App\Filament\Resources\ContabilidadApunteResource;
use Filament\Actions;
use Filament\Resources\Pages\ListRecords;

class ListContabilidadApuntes extends ListRecords
{
    protected static string $resource = ContabilidadApunteResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\CreateAction::make(),
        ];
    }
}
