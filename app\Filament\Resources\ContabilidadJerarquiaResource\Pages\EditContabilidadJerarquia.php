<?php

namespace App\Filament\Resources\ContabilidadJerarquiaResource\Pages;

use App\Filament\Resources\ContabilidadJerarquiaResource;
use Filament\Actions;
use Filament\Resources\Pages\EditRecord;

class EditContabilidadJerarquia extends EditRecord
{
    protected static string $resource = ContabilidadJerarquiaResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\ViewAction::make(),
            Actions\DeleteAction::make(),
        ];
    }

    protected function getRedirectUrl(): string	{
	    return $this->previousUrl ?? $this->getResource()::getUrl('index');
    }
}
