<?php

namespace App\Http\Resources\V1;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class OrdenFabricacionApiResource extends JsonResource
{
    protected function datosComunes(): array
    {
        return [
            'nombre' => $this->nombre,
            'codigo' => $this->codigo,
            'alt_id' => $this->alt_id,
            'fecha' => ($this->fecha) ? $this->fecha->toDateString() : null,
            'fecha_vencimiento' => ($this->fecha_vencimiento) ? $this->fecha_vencimiento->toDateString() : null,
            'fecha_liberacion' => ($this->fecha_liberacion) ? $this->fecha_liberacion->toDateString() : null,
            'estado' => $this->estado?->value,
            'progreso' => $this->progreso,
            'empleado_id' => $this->empleado_id,
            'almacen_id' => $this->almacen_id,
            'proyecto_id' => $this->proyecto_id,
            'proyecto_linea_id' => $this->proyecto_linea_id,
            'descripcion' => $this->descripcion,
            'empresa_id' => $this->empresa_id,
        ];
    }

    public function toArray(Request $request): array
    {
         return [
            'type' => 'ordenFabricacion',
            'id' => $this->id,
            'attributes' => $this->datosComunes(),
        ];
    }

    public function toCsvArray(Request $request): array
    {
        return array_merge([
            'id' => $this->id,
        ], $this->datosComunes());
    }
}
