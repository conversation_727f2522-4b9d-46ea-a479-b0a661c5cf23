<?php

namespace App\Filament\Resources\ContabilidadAsientoResource\Pages;

use App\Filament\Resources\ContabilidadAsientoResource;
use App\Models\ContabilidadAsiento;
use Filament\Actions;
use Filament\Resources\Pages\EditRecord;

class EditContabilidadAsiento extends EditRecord
{
    protected static string $resource = ContabilidadAsientoResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\ViewAction::make(),
            Actions\DeleteAction::make(),
        ];
    }

    protected function mutateFormDataBeforeFill(array $data): array
    {
        return ContabilidadAsiento::onCreate($data);
    }

    protected function getRedirectUrl(): string	{
	    return $this->previousUrl ?? $this->getResource()::getUrl('index');
    }
}
