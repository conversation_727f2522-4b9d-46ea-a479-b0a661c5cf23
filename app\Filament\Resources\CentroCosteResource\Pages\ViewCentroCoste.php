<?php

namespace App\Filament\Resources\CentroCosteResource\Pages;

use App\Filament\Resources\CentroCosteResource;
use App\Filament\Resources\CentroCosteResource\Widgets\CentroCosteEvolucionChart;
use Filament\Actions;
use Filament\Resources\Pages\ViewRecord;

class ViewCentroCoste extends ViewRecord
{
    protected static string $resource = CentroCosteResource::class;

    public function getTitle(): string  {
        return $this->record->nombre;
    }

    protected function getHeaderActions(): array
    {
        return [
            Actions\EditAction::make(),
        ];
    }

    protected function getHeaderWidgets():array
    {
        return [
            CentroCosteEvolucionChart::make(['centroCosteId' => $this->record->id]),
        ];
    }
}
