<?php

namespace App\Filament\Resources\AlbaranResource\Pages;

use App\Filament\Resources\AlbaranResource;
use Filament\Actions;
use Filament\Resources\Pages\ViewRecord;

class ViewAlbaran extends ViewRecord
{
    protected static string $resource = AlbaranResource::class;

    public function getTitle(): string  {
        return '<PERSON><PERSON><PERSON> de ' . $this->record->tipo . ": " . $this->record->num;
    }

    protected function getHeaderActions(): array
    {
        return [
            Actions\Action::make('ver_lista')
                ->label('Ver Albaranes')
                ->icon('heroicon-s-arrow-left')
                ->color('secondary')
                ->url(fn () => url('/albarans')),
            Actions\EditAction::make()
                ->tooltip('Editar'),
        ];
    }
}
