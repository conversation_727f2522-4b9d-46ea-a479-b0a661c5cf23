<?php

namespace App\Filament\Resources\BancoTipoMovimientoResource\Pages;

use App\Filament\Resources\BancoTipoMovimientoResource;
use Filament\Actions;
use Filament\Resources\Pages\CreateRecord;

class CreateBancoTipoMovimiento extends CreateRecord
{
    protected static string $resource = BancoTipoMovimientoResource::class;

    protected function getRedirectUrl(): string
    {
        return $this->getResource()::getUrl('index');
    }
}
