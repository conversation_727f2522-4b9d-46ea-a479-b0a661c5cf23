<?php

namespace App\Traits;

use App\Models\Empleado;
use App\Models\User;
use Illuminate\Database\Eloquent\Builder;

trait HasGranularPermissions
{
    /**
     * Verifica si el usuario puede realizar una acción con restricciones
     */
    public function canWithRestrictions(User $user, string $permission, string $modelName): array
    {
        // Verificar si tiene el permiso básico
        if (!$user->can($permission)) {
            return ['can' => false, 'restriction' => null];
        }

        // Verificar si tiene restricciones granulares
        if ($user->can("only_own_{$modelName}")) {
            return ['can' => true, 'restriction' => 'only_own'];
        }

        if ($user->can("only_team_{$modelName}")) {
            return ['can' => true, 'restriction' => 'only_team'];
        }

        // Si no tiene restricciones granulares, acceso completo
        return ['can' => true, 'restriction' => null];
    }

    /**
     * Aplica filtros de permisos granulares a una consulta
     */
    public function applyGranularFilters(Builder $query, User $user, string $modelName): Builder
    {
        // Verificar permisos con restricciones
        $viewResult = $this->canWithRestrictions($user, "view_any_{$modelName}", $modelName);

        // Si puede ver todos sin restricciones, no aplicar filtros
        if ($viewResult['can'] && !$viewResult['restriction']) {
            return $query;
        }

        // Si no puede ver nada, bloquear todo
        if (!$viewResult['can']) {
            return $query->whereRaw('1 = 0');
        }

        $empleado = Empleado::where('user_id', $user->id)->first();

        if (!$empleado) {
            // Si no tiene empleado asociado, no puede ver nada
            return $query->whereRaw('1 = 0');
        }

        // Aplicar filtros según el tipo de restricción
        switch ($viewResult['restriction']) {
            case 'only_own':
                return $this->applyOwnFilter($query, $user, $empleado);

            case 'only_team':
                return $this->applyTeamFilter($query, $user, $empleado);

            default:
                return $query->whereRaw('1 = 0');
        }
    }

    /**
     * Aplica filtro para ver solo registros propios
     */
    protected function applyOwnFilter(Builder $query, User $user, Empleado $empleado): Builder
    {
        return $query->where(function ($q) use ($user, $empleado) {
            $q->where('a_empleado_id', $empleado->id)  // Asignados al empleado
              ->orWhere('created_by', $user->id);      // Creados por el usuario
        });
    }

    /**
     * Aplica filtro para ver registros del equipo
     */
    protected function applyTeamFilter(Builder $query, User $user, Empleado $empleado): Builder
    {
        // Obtener los equipos del usuario
        $equiposUsuario = \App\Models\EquipoUsuario::where('user_id', $user->id)->pluck('equipo_id');

        if ($equiposUsuario->isEmpty()) {
            // Si no pertenece a ningún equipo, solo puede ver los suyos
            return $this->applyOwnFilter($query, $user, $empleado);
        }

        // Obtener todos los usuarios que pertenecen a los mismos equipos
        $usuariosDelEquipo = \App\Models\EquipoUsuario::whereIn('equipo_id', $equiposUsuario)->pluck('user_id');

        // Obtener los empleados correspondientes a esos usuarios
        $empleadosDelEquipo = \App\Models\Empleado::whereIn('user_id', $usuariosDelEquipo)->pluck('id');

        return $query->where(function ($q) use ($user, $empleado, $empleadosDelEquipo) {
            $q->where('a_empleado_id', $empleado->id)  // Propios (asignados al empleado)
              ->orWhere('created_by', $user->id)       // Creados por él
              ->orWhereIn('a_empleado_id', $empleadosDelEquipo); // Asignados a compañeros de equipo
        });
    }


}
