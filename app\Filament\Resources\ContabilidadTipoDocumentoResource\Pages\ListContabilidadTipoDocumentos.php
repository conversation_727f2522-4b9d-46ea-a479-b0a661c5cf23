<?php

namespace App\Filament\Resources\ContabilidadTipoDocumentoResource\Pages;

use App\Filament\Resources\ContabilidadTipoDocumentoResource;
use Filament\Actions;
use Filament\Resources\Pages\ListRecords;

class ListContabilidadTipoDocumentos extends ListRecords
{
    protected static string $resource = ContabilidadTipoDocumentoResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\CreateAction::make(),
        ];
    }
}
