<?php

namespace App\Filament\Resources\AplicacionResource\Pages;

use App\Filament\Resources\AplicacionResource;
use Filament\Actions;
use Filament\Resources\Pages\ViewRecord;

class ViewAplicacion extends ViewRecord
{
    protected static string $resource = AplicacionResource::class;

    public function getTitle(): string  {
        return $this->record->nombre;
    }

    protected function getHeaderActions(): array
    {
        return [
            Actions\EditAction::make(),
        ];
    }
}
