<?php

namespace App\Filament\Resources\ContabilidadApunteSplitResource\Pages;

use App\Filament\Resources\ContabilidadApunteSplitResource;
use Filament\Actions;
use Filament\Resources\Pages\EditRecord;

class EditContabilidadApunteSplit extends EditRecord
{
    protected static string $resource = ContabilidadApunteSplitResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\ViewAction::make(),
            Actions\DeleteAction::make(),
        ];
    }

    protected function getRedirectUrl(): string	{
	    return $this->previousUrl ?? $this->getResource()::getUrl('index');
    }
}
