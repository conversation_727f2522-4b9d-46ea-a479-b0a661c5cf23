<?php

namespace App\Http\Controllers\Api\V1;

//use App\Http\Filters\V1\OrdenFabricacionSalidaFilter;
use App\Http\Resources\V1\OrdenFabricacionSalidaApiResource;
use App\Models\OrdenFabricacionSalida;
use App\Policies\V1\OrdenFabricacionPolicy;

class OrdenFabricacionSalidaController extends BaseApiController
{
    protected $policyClass = OrdenFabricacionPolicy::class;
    protected $modelClass = OrdenFabricacionSalida::class;
    protected $resourceClass = OrdenFabricacionSalidaApiResource::class;
    protected $filterClass = null;
    protected $withRelations = [];
}

