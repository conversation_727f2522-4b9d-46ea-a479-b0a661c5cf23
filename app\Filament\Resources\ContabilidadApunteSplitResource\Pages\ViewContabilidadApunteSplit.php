<?php

namespace App\Filament\Resources\ContabilidadApunteSplitResource\Pages;

use App\Filament\Resources\ContabilidadApunteSplitResource;
use Filament\Actions;
use Filament\Resources\Pages\ViewRecord;

class ViewContabilidadApunteSplit extends ViewRecord
{
    protected static string $resource = ContabilidadApunteSplitResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\EditAction::make(),
        ];
    }
}
