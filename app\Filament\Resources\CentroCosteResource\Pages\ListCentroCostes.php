<?php

namespace App\Filament\Resources\CentroCosteResource\Pages;

use App\Filament\Resources\CentroCosteResource;
use App\Filament\Resources\CentroCosteResource\Widgets\CentrosCosteEvolucionChart;
use App\Filament\Resources\CentroCosteResource\Widgets\CentrosCosteFacturacionChart;
use Filament\Actions;
use Filament\Resources\Pages\ListRecords;

class ListCentroCostes extends ListRecords
{
    protected static string $resource = CentroCosteResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\CreateAction::make(),
        ];
    }

    protected function getHeaderWidgets():array
    {
        return [
            CentrosCosteEvolucionChart::class,
            CentrosCosteFacturacionChart::class,
        ];
    }
}
