<?php

namespace App\Filament\Resources\BancoMovimientoResource\Pages;

use App\Filament\Resources\BancoMovimientoResource;
use Filament\Actions;
use Filament\Resources\Pages\ListRecords;

class ListBancoMovimientos extends ListRecords
{
    protected static string $resource = BancoMovimientoResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\CreateAction::make(),
        ];
    }
}
