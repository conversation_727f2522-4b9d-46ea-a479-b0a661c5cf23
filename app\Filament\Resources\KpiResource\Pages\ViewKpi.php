<?php

namespace App\Filament\Resources\KpiResource\Pages;

use App\Filament\Resources\KpiResource\Widgets\KpiEvolucionChart;
use App\Filament\Resources\KpiResource;
use App\Filament\Resources\KpiResource\Widgets\KpiGaugeChart;
use Filament\Actions;
use Filament\Resources\Pages\ViewRecord;

class ViewKpi extends ViewRecord
{
    protected static string $resource = KpiResource::class;

    public function getTitle(): string  {
        return $this->record->nombre;
    }

    protected function getHeaderActions(): array
    {
        return [
            Actions\EditAction::make(),
        ];
    }

    protected function getHeaderWidgets():array
    {
        return [
            KpiEvolucionChart::make(['kpiId' => $this->record->id]),
            KpiGaugeChart::make(['kpiId' => $this->record->id]),
        ];
    }
}
