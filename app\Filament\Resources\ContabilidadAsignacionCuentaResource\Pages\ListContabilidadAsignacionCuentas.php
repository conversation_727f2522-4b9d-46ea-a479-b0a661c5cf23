<?php

namespace App\Filament\Resources\ContabilidadAsignacionCuentaResource\Pages;

use App\Filament\Resources\ContabilidadAsignacionCuentaResource;
use Filament\Actions;
use Filament\Resources\Pages\ListRecords;

class ListContabilidadAsignacionCuentas extends ListRecords
{
    protected static string $resource = ContabilidadAsignacionCuentaResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\CreateAction::make(),
        ];
    }
}
