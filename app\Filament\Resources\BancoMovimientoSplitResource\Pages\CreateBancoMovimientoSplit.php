<?php

namespace App\Filament\Resources\BancoMovimientoSplitResource\Pages;

use App\Filament\Resources\BancoMovimientoSplitResource;
use Filament\Actions;
use Filament\Resources\Pages\CreateRecord;

class CreateBancoMovimientoSplit extends CreateRecord
{
    protected static string $resource = BancoMovimientoSplitResource::class;

    protected function getRedirectUrl(): string
    {
        return $this->getResource()::getUrl('index');
    }
}
