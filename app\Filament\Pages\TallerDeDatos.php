<?php

namespace App\Filament\Pages;

use App\Models\Empresa;
use App\Models\Factura;
use Filament\Pages\Page;
use Filament\Widgets\Widget;
use Illuminate\Support\Facades\DB;
use Filament\Forms\Form;
use Filament\Forms\Components\Textarea;
use Filament\Actions\Action;
use Illuminate\Support\Facades\Http;

class TallerDeDatos extends Page
{
    protected static ?string $navigationIcon = 'heroicon-o-table-cells';
    protected static string $view = 'filament.pages.taller-datos';

    /**
     * Determina si el usuario puede acceder a esta página
     * Requiere al menos uno de los permisos de los módulos
     */
    public static function canAccess(): bool
    {
        return getPermission('ContabilidadApunte', 'canViewAny') ||
               getPermission('Factura', 'canViewAny') ||
               getPermission('ContabilidadPresupuesto', 'canViewAny');
    }

    public array $facturasventa = [];
    public array $facturascompra = [];
    public array $cuentaresultados = [];
    public array $balance = [];
    public array $presupuestos = [];
    public $tipo = "json";
    public array $ficheros = [];

    public function mount()
    {
        $empresaId = session()->get('empresa_id');
        $uuid = Empresa::find($empresaId)?->uuid;

        // Inicializar arrays vacíos por defecto
        $this->facturasventa = [];
        $this->facturascompra = [];
        $this->cuentaresultados = [];
        $this->balance = [];
        $this->presupuestos = [];
        $this->ficheros = [];

        // genFacturasVenta - requiere permiso view_any_contabilidad::apunte
        if (getPermission('ContabilidadApunte', 'canViewAny')) {
            $this->genFacturasVenta($empresaId, $uuid . '_factventa.csv');
        }

        // genFacturasCompra - requiere permiso view_any_factura
        if (getPermission('Factura', 'canViewAny')) {
            $this->genFacturasCompra($empresaId, $uuid . '_factcompra.csv');
        }

        // genCuentaResultados - requiere permiso view_any_contabilidad::apunte
        if (getPermission('ContabilidadApunte', 'canViewAny')) {
            $this->genCuentaResultados($empresaId, $uuid . '_ctares.csv');
        }

        // genBalance - requiere permiso view_any_contabilidad::apunte
        if (getPermission('ContabilidadApunte', 'canViewAny')) {
            $this->genBalance($empresaId, $uuid . '_balance.csv');
        }

        // genPresupuestos - requiere permiso view_any_contabilidad::presupuesto
        if (getPermission('ContabilidadPresupuesto', 'canViewAny')) {
            $this->genPresupuestos($empresaId, $uuid . '_presupuesto.csv');
        }
    }

    protected function exportData(array $data, $filename = null)
    {
        if ($this->tipo === 'json') {
            //dd($data);
            return $data; // Simplemente devolvemos el array
        } elseif ($this->tipo === 'csv' && $filename) {
            $filepath = storage_path("app/public/$filename");
            $handle = fopen($filepath, 'w');
            if (!empty($data)) {
                fputcsv($handle, array_keys($data[0])); // Encabezados

                foreach ($data as $row) {
                    fputcsv($handle, $row);
                }
            } else {
                fputcsv($handle, ["Sin datos"]);
            }
            fclose($handle);

            return [];
        }
    }


    protected function genFacturasVenta($empresaId, $fichero=null){
        $datos = DB::select("
            SELECT f.fecha, f.estado,
                p.nombre AS cliente, seg.nombre AS segmento,
                e.nombre AS comercial,
                pr.nombre AS proyecto,
                cc.nombre AS centro_coste,
                l.importe, l.iva, l.total,
                a.nombre AS articulo, fam1.familia, fam1.subfamilia
            FROM lineas l
            INNER JOIN facturas f ON l.padre_id = f.id
            INNER JOIN personas p ON f.persona_id = p.id
            LEFT JOIN segmentos seg ON p.segmento_id = seg.id
            LEFT JOIN empleados e ON f.empleado_id = e.id
            LEFT JOIN empleados e2 ON p.empleado_id = e2.id
            LEFT JOIN articulos a ON l.articulo_id = a.id
            LEFT JOIN articulo_familias fam1 ON a.articulo_familia_id = fam1.id
            LEFT JOIN proyectos pr ON f.proyecto_id = pr.id
            LEFT JOIN centro_costes cc ON pr.centro_coste_id = cc.id
            WHERE f.tipo = 'Venta'
            AND l.empresa_id = " . $empresaId
        );

        $datos = collect($datos)->map(fn($factura) => [
            'fecha' => $factura->fecha,
            'estado' => $factura->estado,
            'cliente' => $factura->cliente,
            'segmento' => $factura->segmento,
            'comercial' => $factura->comercial,
            'proyecto' => $factura->proyecto,
            'centro_coste' => $factura->centro_coste,
            'importe' => $factura->importe,
            'iva' => $factura->iva,
            'total' => $factura->total,
            'articulo' => $factura->articulo,
            'familia' => $factura->familia,
            'subfamilia' => $factura->subfamilia,
        ])->toArray();
        $this->facturasventa = $this->exportData($datos, $fichero);
        $this->ficheros['facturasventa']= $fichero;
    }

    protected function genFacturasCompra($empresaId, $fichero=null){
        $datos = DB::select("
            SELECT f.fecha, f.estado,
                p.nombre AS proveedor,
                e.nombre AS empleado_factura,
                pr.nombre AS proyecto,
                cc.nombre AS centro_coste,
                l.importe, l.iva, l.total,
                a.nombre AS articulo, fam1.familia, fam1.subfamilia
            FROM lineas l
            INNER JOIN facturas f ON l.padre_id = f.id
            INNER JOIN personas p ON f.persona_id = p.id
            LEFT JOIN empleados e ON f.empleado_id = e.id
            LEFT JOIN empleados e2 ON p.empleado_id = e2.id
            LEFT JOIN articulos a ON l.articulo_id = a.id
            LEFT JOIN articulo_familias fam1 ON a.articulo_familia_id = fam1.id
            LEFT JOIN proyectos pr ON f.proyecto_id = pr.id
            LEFT JOIN centro_costes cc ON pr.centro_coste_id = cc.id
            WHERE f.tipo = 'Compra'
            AND l.empresa_id = " . $empresaId
        );

        $datos = collect($datos)->map(fn($factura) => [
            'fecha' => $factura->fecha,
            'estado' => $factura->estado,
            'proveedor' => $factura->proveedor,
            'empleado_factura' => $factura->empleado_factura,
            'proyecto' => $factura->proyecto,
            'centro_coste' => $factura->centro_coste,
            'importe' => $factura->importe,
            'iva' => $factura->iva,
            'total' => $factura->total,
            'articulo' => $factura->articulo,
            'familia' => $factura->familia,
            'subfamilia' => $factura->subfamilia,
        ])->toArray();
        $this->facturascompra = $this->exportData($datos, $fichero);
        $this->ficheros['facturascompra']= $fichero;
    }

    protected function genCuentaResultados($empresaId, $fichero=null){
        $datos = DB::select("
            select v.fecha
            , masa, pgc1, pgc2, pgc3, n1, n2, n3, cuenta_full
            , centro_coste
            , sum(-variacion) as variacion
            , sum(-variacion_real) as variacion_real
            from vwkpicontabilidad v
            where masa in('Gastos', 'Ingresos') and v.empresa_id = " . $empresaId .
            " group by 1,2,3,4,5,6,7,8,9,10"
        );

        $datos = collect($datos)->map(fn($rec) => [
            'fecha' => $rec->fecha,
            'masa' => $rec->masa,
            'pgc1' => $rec->pgc1,
            'pgc2' => $rec->pgc2,
            'pgc3' => $rec->pgc3,
            'nivel1' => $rec->n1,
            'nivel2' => "_ " . $rec->n2,
            'nivel3' => "__ " .$rec->n3,
            'cuenta' => $rec->cuenta_full,
            'centro_coste' => $rec->centro_coste,
            'variacion' => $rec->variacion,
            'variacion_real' => $rec->variacion_real,
        ])->toArray();
        $this->cuentaresultados = $this->exportData($datos, $fichero);
        $this->ficheros['cuentaresultados']= $fichero;
        //dd($datos);
    }

    protected function genBalance($empresaId, $fichero=null){
        $datos = DB::select("
            select v.fecha
            , masa, pgc1, n1, n2, left(n3, 50) as n3, cuenta_full
            , centro_coste
            , sum(variacion) as variacion
            , sum(variacion_real) as variacion_real
            from vwkpicontabilidad v
            where masa in('Activo', 'Pasivo') and v.empresa_id = " . $empresaId .
            " group by 1,2,3,4,5,6,7,8 " .
            " having sum(variacion) <> 0 or sum(variacion_real) <> 0"
        );

        $datos = collect($datos)->map(fn($rec) => [
            'fecha' => $rec->fecha,
            'masa' => $rec->masa,
            'pgc1' => $rec->pgc1,
            'nivel1' => $rec->n1,
            'nivel2' => $rec->n2,
            //'nivel3' => $rec->n3,
            //'cuenta' => $rec->cuenta_full,
            'centro_coste' => $rec->centro_coste,
            'variacion' => $rec->variacion,
            'variacion_real' => $rec->variacion_real,
        ])->toArray();
        $this->balance = $this->exportData($datos, $fichero);
        $this->ficheros['balance']= $fichero;
        //dd($datos);
    }

    protected function genPresupuestos($empresaId, $fichero=null){
        $datos = DB::select(
            "select concat(p.codigo,'/', p.version, ' ', p.nombre) as presupuesto
            , concat(g.numero, ' ', g.nombre) as grupo
            , concat(g.numero, '.', sg.numero, ' ', sg.nombre) as subgrupo
            , af.familia, af.subfamilia
            , a.nombre as articulo
            , sum(l.cantidad_final) as cantidad
            , sum(l.importe_coste) as coste
            , sum(l.importe_venta) as ingreso
            , sum(importe_venta-importe_coste) as margen_bruto
            from presupuestos p
            inner join personas p2 on p.persona_id = p2.id
            inner join presupuesto_lineas l on l.presupuesto_id = p.id
            left join presupuesto_grupos sg on l.presupuesto_grupo_id = sg.id
            left join presupuesto_grupos g on sg.presupuesto_grupo_id = g.id
            left join articulos a on l.articulo_id = a.id
            left join articulo_familias af on a.articulo_familia_id = af.id
            where p.empresa_id = " . $empresaId .
            " group by 1,2,3,4,5,6");

        $datos = collect($datos)->map(fn($rec) => [
            'presupuesto' => $rec->presupuesto,
            'grupo' => $rec->grupo,
            'subgrupo' => $rec->subgrupo,
            'familia' => $rec->familia,
            'subfamilia' => $rec->subfamilia,
            'articulo' => $rec->articulo,
            'cantidad' => $rec->cantidad,
            'coste' => $rec->coste,
            'ingreso' => $rec->ingreso,
            'margen_bruto' => $rec->margen_bruto,
        ])->toArray();
        $this->presupuestos = $this->exportData($datos, $fichero);
        $this->ficheros['presupuestos']= $fichero;
       //dd($datos);
    }

}



