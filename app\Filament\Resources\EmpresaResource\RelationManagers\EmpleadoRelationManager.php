<?php

namespace App\Filament\Resources\EmpresaResource\RelationManagers;

use App\Models\Empleado;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\RelationManagers\RelationManager;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;

class EmpleadoRelationManager extends RelationManager
{
    protected static string $relationship = 'Empleado';
    protected static ?string $title = 'Empleados';
    public function isReadOnly(): bool { return false; }

    public function form(Form $form): Form
    {
        return $form
            ->schema(Empleado::getForm($this->ownerRecord->id));
    }

    public function table(Table $table): Table
    {
        return $table
            ->columns(Empleado::getTabla(null, $this->ownerRecord->id))
            ->filters([
                //
            ])
            ->headerActions([
                Tables\Actions\CreateAction::make(),
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
                Tables\Actions\DeleteAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }
}
