<?php

namespace App\Filament\Resources\ArticuloFamiliaResource\Widgets;

use Filament\Support\RawJs;
use Illuminate\Support\Facades\DB;
use Leandrocfe\FilamentApexCharts\Widgets\ApexChartWidget;

class FamiliasPieChart extends ApexChartWidget
{
    /**
     * Chart Id
     *
     * @var string
     */
    protected static ?string $chartId = 'familiasPieChart';

    /**
     * Widget Title
     *
     * @var string|null
     */
    protected static ?string $heading = 'Facturación por familia';

    /**
     * Chart options (series, labels, types, size, animations...)
     * https://apexcharts.com/docs/options
     *
     * @return array
     */

     protected function getOptions(): array
     {
        $empresaId = session()->get('empresa_id');
        $registros = DB::select(
            'SELECT ifnull(af.familia, \'Otros\') as familia
	        , sum(l.importe) as importe
            from lineas l
	        inner join facturas f on l.padre_id = f.id
	        left join articulos a on l.articulo_id = a.id
	        left join articulo_familias af on a.articulo_familia_id = af.id
	        where l.empresa_id = ? and l.padre_type =\'App\\\\Models\\\\Factura\'  and f.tipo = "Venta"
	        group by 1 order by 1',
            [$empresaId]
        );
        $labels = array_map(fn($row) => $row->familia, $registros);
        $data = array_map(fn($row) => floatval($row->importe), $registros);

        return [
            'chart' => [
                'type' => 'pie',
                'height' => 350,
            ],
            'series' => $data,
            'labels' => $labels,
            'legend' => [
                'position' => 'bottom',
            ],
        ];
    }

    protected function extraJsOptions(): ?RawJs
    {
        return RawJs::make(<<<'JS'
        {
            yaxis: {
                labels: {
                    formatter: function (val, index) {
                        return new Intl.NumberFormat('es-ES', {
                            maximumFractionDigits: 0
                        }).format(val)
                    }
                }
            },
            dataLabels: {
                enabled: false,
            }
        }
        JS);
    }
}
