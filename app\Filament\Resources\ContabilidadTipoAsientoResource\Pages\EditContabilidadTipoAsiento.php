<?php

namespace App\Filament\Resources\ContabilidadTipoAsientoResource\Pages;

use App\Filament\Resources\ContabilidadTipoAsientoResource;
use Filament\Actions;
use Filament\Resources\Pages\EditRecord;

class EditContabilidadTipoAsiento extends EditRecord
{
    protected static string $resource = ContabilidadTipoAsientoResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\ViewAction::make(),
            Actions\DeleteAction::make(),
        ];
    }

    protected function getRedirectUrl(): string	{
	    return $this->previousUrl ?? $this->getResource()::getUrl('index');
    }
}
