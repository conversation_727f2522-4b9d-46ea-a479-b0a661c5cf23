<?php

namespace App\Filament\Resources\ContabilidadCuentaResource\Pages;

use App\Filament\Resources\ContabilidadCuentaResource;
use App\Models\ContabilidadCuenta;
use Filament\Actions;
use Filament\Resources\Pages\EditRecord;

class EditContabilidadCuenta extends EditRecord
{
    protected static string $resource = ContabilidadCuentaResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\ViewAction::make(),
            Actions\DeleteAction::make(),
        ];
    }

    protected function mutateFormDataBeforeFill(array $data): array
    {
        return ContabilidadCuenta::onCreate($data);
    }

    protected function getRedirectUrl(): string	{
	    return $this->previousUrl ?? $this->getResource()::getUrl('index');
    }
}
