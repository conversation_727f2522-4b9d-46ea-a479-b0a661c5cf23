<?php

namespace App\Http\Controllers\Api\V1;

//use App\Http\Filters\V1\OrdenFabricacionFilter;
use App\Http\Resources\V1\OrdenFabricacionApiResource;
use App\Models\OrdenFabricacion;
use App\Policies\V1\OrdenFabricacionPolicy;

class OrdenFabricacionController extends BaseApiController
{
    protected $policyClass = OrdenFabricacionPolicy::class;
    protected $modelClass = OrdenFabricacion::class;
    protected $resourceClass = OrdenFabricacionApiResource::class;
    protected $filterClass = null;
    protected $withRelations = [];
}

