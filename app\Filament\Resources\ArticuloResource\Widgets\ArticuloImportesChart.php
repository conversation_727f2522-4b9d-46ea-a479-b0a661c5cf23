<?php

namespace App\Filament\Resources\ArticuloResource\Widgets;

use Filament\Forms\Components\Select;
use Filament\Support\RawJs;
use Illuminate\Support\Facades\DB;
use Leandrocfe\FilamentApexCharts\Widgets\ApexChartWidget;

class ArticuloImportesChart extends ApexChartWidget
{
    /**
     * Chart Id
     *
     * @var string
     */
    protected static ?string $chartId = 'articuloImportesChart';

    /**
     * Widget Title
     *
     * @var string|null
     */
    protected static ?string $heading = 'Importe por trimestre';

    public $articuloId;

     protected function getOptions(): array
     {
        $tipo = $this->form->getState()['tipo'] ?? 'venta';
        $registros = DB::select(
            'SELECT concat(year(f.fecha),\'-\', QUARTER(f.fecha)) as x
            , f.tipo as serie
	        , sum(l.importe) as valor
	        from lineas l
	        inner join facturas f on l.padre_id = f.id and l.padre_type =\'App\\\\Models\\\\Factura\'  and f.tipo = "Venta"
	        inner join articulos a on l.articulo_id = a.id
	        where a.id =?
	        group by 1,2
            order by 1,2',
            [$this->articuloId]
        );

        $data = [];
        foreach ($registros as $registro) {
            $data[$registro->x][$registro->serie] = floatval($registro->valor);
        }

        $xaxis = array_keys($data);
        $nombreseries = [];
        foreach ($data as $serie_data) {
            $nombreseries = array_unique(array_merge($nombreseries, array_keys($serie_data)));
        }

        $series = [];
        foreach ($nombreseries as $serie_nombre) {
            $serie_data = [];
            foreach ($xaxis as $x) {
                $serie_data[] = $data[$x][$serie_nombre] ?? 0;
            }
            $series[] = [
                'name' => $serie_nombre,
                'data' => $serie_data,
            ];
        }

        return [
             'chart' => [
                 'type' => 'bar',
                 'height' => 300,
                 'stacked' => false,
             ],
             'series' => $series,
             'xaxis' => [
                 'categories' =>  $xaxis,
                 'labels' => [
                     'style' => [
                         'fontFamily' => 'inherit',
                     ],
                 ],
             ],
             'yaxis' => [
                 'labels' => [
                     'style' => [
                         'fontFamily' => 'inherit',
                     ],
                 ],
            ],
            'colors' => ['#f59e0b', '#FF5733'],
            'plotOptions' => [
                'bar' => [
                    'borderRadius' => 3,
                    'horizontal' => false,
                ],
            ],
        ];
    }

    protected function extraJsOptions(): ?RawJs
    {
        return RawJs::make(<<<'JS'
        {
            yaxis: {
                labels: {
                    formatter: function (val, index) {
                        return new Intl.NumberFormat('es-ES', {
                            maximumFractionDigits: 0
                        }).format(val)
                    }
                }
            },
            dataLabels: {
                enabled: false,
            }
        }
        JS);
    }
}
