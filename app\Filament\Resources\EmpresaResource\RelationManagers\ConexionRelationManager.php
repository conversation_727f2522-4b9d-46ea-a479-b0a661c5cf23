<?php

namespace App\Filament\Resources\EmpresaResource\RelationManagers;

use Filament\Forms;
use Filament\Forms\Components\Section;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Form;
use Filament\Resources\RelationManagers\RelationManager;
use Filament\Tables;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;
use Illuminate\Database\Eloquent\Model;

class ConexionRelationManager extends RelationManager
{
    protected static string $relationship = 'Conexion';
    protected static ?string $title = 'Conexiones';

    public function isReadOnly(): bool { return false; }

    public function form(Form $form): Form
    {
        return $form
            ->schema([
                Select::make('aplicacion_id')
                    ->relationship('aplicacion', 'nombre')
                    ->required(),
                Section::make('Datos de la conexión')
                    ->columns(2)
                    ->schema([
                        TextInput::make('id_externo')
                            ->maxLength(36),
                        TextInput::make('host')
                            ->maxLength(50),
                        TextInput::make('puerto')
                            ->numeric(),
                        TextInput::make('db')
                            ->maxLength(50),
                        TextInput::make('usuario')
                            ->maxLength(50),
                        TextInput::make('password')
                            ->password()
                            ->maxLength(50),
                    ]),
            ]);
    }

    public function table(Table $table): Table
    {
        return $table
            ->striped()
            ->columns([
                TextColumn::make('aplicacion.nombre')
                    ->sortable(),
                TextColumn::make('id_externo')
                    ->searchable(),
                TextColumn::make('host')
                    ->searchable(),
                TextColumn::make('puerto')
                    ->searchable(),
                TextColumn::make('db')
                    ->searchable(),
                TextColumn::make('instancia')
                    ->searchable(),
                TextColumn::make('usuario')
                    ->searchable(),
                    ])
            ->filters([
                //
            ])
            ->headerActions([
                Tables\Actions\CreateAction::make(),
            ])
            ->actions([
                Tables\Actions\EditAction::make()
					->label(false)
					->tooltip('Editar'),
				Tables\Actions\DeleteAction::make()
					->label(false)
					->tooltip('Borrar'),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }

    public static function canViewForRecord(Model $ownerRecord, string $pageClass): bool { return getPermission(static::class, 'canView', static::$relationship); }
    public function canViewAny(): bool {return getPermission(get_class(), __FUNCTION__);}
    public function canEdit($record): bool {return getPermission(get_class(), __FUNCTION__);}
    public function canCreate(): bool {return getPermission(get_class(), __FUNCTION__);}
    public function canDelete($record): bool {return getPermission(get_class(), __FUNCTION__);}
    public function canDeleteAny(): bool {return getPermission(get_class(), __FUNCTION__);}
}
