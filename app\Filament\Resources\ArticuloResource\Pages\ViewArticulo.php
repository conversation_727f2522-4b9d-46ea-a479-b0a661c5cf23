<?php

namespace App\Filament\Resources\ArticuloResource\Pages;

use App\Filament\Resources\ArticuloResource;
use App\Filament\Resources\ArticuloResource\Widgets\ArticuloImportesChart;
use App\Filament\Resources\ArticuloResource\Widgets\ArticuloPieChart;
use App\Models\Articulo;
use Filament\Actions;
use Filament\Resources\Pages\ViewRecord;

class ViewArticulo extends ViewRecord
{
    protected static string $resource = ArticuloResource::class;

    public function getTitle(): string  {
        return $this->record->nombre;
    }

    protected function getHeaderActions(): array
    {
        return [
            Actions\EditAction::make(),
        ];
    }

    protected function getHeaderWidgets():array
    {
        return [
            ArticuloImportesChart::make(['articuloId' => $this->record->id]),
            ArticuloPieChart::make(['articuloId' => $this->record->id]),
        ];
    }
}
