<?php

namespace App\Filament\Resources\FicheroResource\Pages;

use App\Filament\Resources\FicheroResource;
use Filament\Actions;
use Filament\Resources\Pages\ViewRecord;

class ViewFichero extends ViewRecord
{
    protected static string $resource = FicheroResource::class;

    public function getTitle(): string  {
        return $this->record->nombre;
    }

    protected function getHeaderActions(): array
    {
        return [
            Actions\Action::make('ver_lista')
                ->label('Ver Ficheros')
                ->icon('heroicon-s-arrow-left')
                ->color('secondary')
                ->url(fn () => url('/ficheroes')),
            Actions\EditAction::make(),
        ];
    }
}
