<?php

namespace App\Filament\Resources\ContabilidadCuentaResource\RelationManagers;

use App\Models\ContabilidadPresupuesto;
use Filament\Forms\Form;
use Filament\Resources\RelationManagers\RelationManager;
use Filament\Tables;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Filters\SelectFilter;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;
use Webbingbrasil\FilamentAdvancedFilter\Filters\NumberFilter;
use Illuminate\Database\Eloquent\Model;

class ContabilidadPresupuestoRelationManager extends RelationManager
{
    protected static string $relationship = 'ContabilidadPresupuesto';
    protected static ?string $title = 'Presupuestos';

    public function isReadOnly(): bool {
        return false;
    }

    public function form(Form $form): Form
    {
        return $form
            ->schema(ContabilidadPresupuesto::getForm($this->getOwnerRecord()->cuenta_id));
    }

    public function table(Table $table): Table
    {
        return $table
            ->striped()
            ->columns([
                TextColumn::make('ejercicio')
                    ->sortable(),
                TextColumn::make('centroCoste.nombre')
                ->sortable(),
                TextColumn::make('mes01')->numeric()->label('Ene'),
                TextColumn::make('mes02')->numeric()->label('Feb'),
                TextColumn::make('mes03')->numeric()->label('Mar'),
                TextColumn::make('mes04')->numeric()->label('Abr'),
                TextColumn::make('mes05')->numeric()->label('May'),
                TextColumn::make('mes06')->numeric()->label('Jun'),
                TextColumn::make('mes07')->numeric()->label('Jul'),
                TextColumn::make('mes08')->numeric()->label('Ago'),
                TextColumn::make('mes09')->numeric()->label('Sep'),
                TextColumn::make('mes10')->numeric()->label('Oct'),
                TextColumn::make('mes11')->numeric()->label('Oct'),
                TextColumn::make('mes12')->numeric()->label('Dic'),
            ])
            ->filters([
                NumberFilter::make('ejercicio'),
                SelectFilter::make('Centro de coste')
                    ->relationship('CentroCoste', 'nombre'),
            ])
            ->persistFiltersInSession()
            ->headerActions([
                Tables\Actions\CreateAction::make(),
            ])
            ->actions([
                Tables\Actions\EditAction::make()
                    ->label(false)
                    ->tooltip('Editar'),
                Tables\Actions\DeleteAction::make()
                    ->label(false)
                    ->tooltip('Borrar'),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }

    public static function canViewForRecord(Model $ownerRecord, string $pageClass): bool { return getPermission(static::class, 'canView', static::$relationship); }
    public function canViewAny(): bool {return getPermission(get_class(), __FUNCTION__);}
    public function canEdit($record): bool {return getPermission(get_class(), __FUNCTION__);}
    public function canCreate(): bool {return getPermission(get_class(), __FUNCTION__);}
    public function canDelete($record): bool {return getPermission(get_class(), __FUNCTION__);}
    public function canDeleteAny(): bool {return getPermission(get_class(), __FUNCTION__);}
}
