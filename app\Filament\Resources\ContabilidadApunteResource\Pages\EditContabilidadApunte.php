<?php

namespace App\Filament\Resources\ContabilidadApunteResource\Pages;

use App\Filament\Resources\ContabilidadApunteResource;
use App\Models\ContabilidadApunte;
use Filament\Actions;
use Filament\Resources\Pages\EditRecord;

class EditContabilidadApunte extends EditRecord
{
    protected static string $resource = ContabilidadApunteResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\ViewAction::make(),
            Actions\DeleteAction::make(),
        ];
    }

    protected function mutateFormDataBeforeFill(array $data): array
    {
        return ContabilidadApunte::onCreate($data);
    }

    protected function getRedirectUrl(): string	{
        //return $this->getResource()::getUrl('view',  ['record' => $this->record]);
	    return $this->previousUrl ?? $this->getResource()::getUrl('index');
    }
}
