<?php

namespace App\Filament\Resources\ContabilidadCuentaResource\Widgets;

use Filament\Forms\Components\Select;
use Filament\Support\RawJs;
use Illuminate\Support\Facades\DB;
use Leandrocfe\FilamentApexCharts\Widgets\ApexChartWidget;

class CuentaImportesChartOld extends ApexChartWidget
{
    protected static ?string $chartId = 'cuentaImportesChart';
    protected static ?string $heading = 'Importes';
    protected static ?string $pollingInterval = '120s';

    public $cuentaId;
    public $selectedYear;
    public $defaultYear;

    protected function getFormSchema(): array  {
        $empresaId = session()->get('empresa_id');
        $maxYear = DB::table('contabilidad_saldos')
            ->selectRaw('MAX(YEAR(fecha)) as max_year')
            ->where('empresa_id', $empresaId)
            ->value('max_year');
        $minYear = DB::table('contabilidad_saldos')
            ->selectRaw('MIN(YEAR(fecha)) as min_year')
            ->where('empresa_id', $empresaId)
            ->value('min_year');
        $this->defaultYear = $maxYear;
        return [
            Select::make('selectedYear')
                ->label('Año')
                ->options(function () use ($minYear, $maxYear) {
                    $currentYear = date('Y');
                    return array_combine(
                        range($maxYear, $minYear),  // Rango descendente
                        range($maxYear, $minYear)
                    );
                })
                ->default($this->defaultYear) // Año por defecto es el actual
                ->live()
                ->afterStateUpdated(function () {
                    $this->getOptions();
                }),
        ];
    }

     protected function getOptions(): array  {
        $selectedYear = $this->filterFormData? $this->filterFormData['selectedYear'] : $this->defaultYear;
        self::$heading = "Importes " . $selectedYear;
        $registros = DB::select(
            'SELECT DATE_FORMAT(s.fecha, \'%Y-%m\') as fecha,
                SUM(s.debe_graph) as D,
		        SUM(s.haber_graph) as H,
		        sum(s.apertura_graph) as A
             FROM contabilidad_saldos s
             WHERE s.contabilidad_cuenta_id = ?  and year(s.fecha) = ?
             GROUP BY 1
             ORDER BY 1',
            [$this->cuentaId, $selectedYear]
        );

        $labels = array_map(fn($row) => $row->fecha, $registros);
        $dataD = array_map(fn($row) => $row->D, $registros);
        $dataH = array_map(fn($row) => $row->H, $registros);
        $dataA = array_map(fn($row) => $row->A, $registros);

          return [
             'chart' => [
                 'type' => 'bar',
                 'height' => 300,
                 'stacked' => true,
             ],
             'series' => [
                 [
                     'name' => 'Debe',
                     'data' =>  $dataD,
                 ],
                 [
                    'name' => 'Haber',
                    'data' =>  $dataH,
                ],
                [
                    'name' => 'Apertura',
                    'data' =>  $dataA,
                ],
             ],
             'xaxis' => [
                 'categories' =>  $labels,
                 'labels' => [
                     'style' => [
                         'fontFamily' => 'inherit',
                     ],
                 ],
             ],
             'yaxis' => [
                 'labels' => [
                     'style' => [
                         'fontFamily' => 'inherit',
                     ],
                 ],
            ],
            'colors' => ['#f59e0b', '#FF5733', '#0077b6'],
            'plotOptions' => [
                'bar' => [
                    'borderRadius' => 3,
                    'horizontal' => false,
                ],
            ],
        ];
    }

    protected function extraJsOptions(): ?RawJs
    {
        return RawJs::make(<<<'JS'
        {
            yaxis: {
                labels: {
                    formatter: function (val, index) {
                        return new Intl.NumberFormat('es-ES', {
                            maximumFractionDigits: 0
                        }).format(val)
                    }
                }
            },
            dataLabels: {
                enabled: false,
            }
        }
        JS);
    }
}
