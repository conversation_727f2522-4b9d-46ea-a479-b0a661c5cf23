<?php

namespace App\Filament\Resources\BancoResource\Pages;

use App\Filament\Resources\BancoResource;
use Filament\Actions;
use Filament\Resources\Pages\EditRecord;

class EditBanco extends EditRecord
{
    protected static string $resource = BancoResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\ViewAction::make(),
            Actions\DeleteAction::make(),
        ];
    }

    protected function getRedirectUrl(): string	{
	    return $this->previousUrl ?? $this->getResource()::getUrl('index');
    }
}
