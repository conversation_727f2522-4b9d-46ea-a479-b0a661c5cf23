<?php

namespace App\Filament\Resources\EmpleadoResource\Widgets;

use Filament\Forms\Components\Select;
use Filament\Support\RawJs;
use Illuminate\Support\Facades\DB;
use Leandrocfe\FilamentApexCharts\Widgets\ApexChartWidget;

class EmpleadoImportesChart extends ApexChartWidget
{
    protected static ?string $chartId = 'empleadoImportesChart';
    protected static ?string $heading = 'Importes';

    public $empleadoId;
    public $selectedYear;
    public $defaultYear;

    protected function getOptions(): array {
        $selectedYear = $this->filterFormData? $this->filterFormData['selectedYear'] : $this->defaultYear;
        self::$heading = "Importes " . $selectedYear;
        $registros = DB::select(
            'SELECT DATE_FORMAT(x.fecha, \'%Y-%m\') as x
                , s.nombre as serie
                , sum(x.facturado) as valor
            from contabilidad_apuntes_extended x
            inner join contabilidad_cuentas c on x.contabilidad_cuenta_id = c.id
            inner join personas p on x.persona_id = p.id
            left join segmentos s on p.segmento_id = s.id
            where facturado <> 0 and p.empleado_id =? AND x.fecha >= DATE_SUB(CURDATE(), INTERVAL 24 MONTH)
            GROUP BY 1,2
            ORDER BY 1,2',
            [$this->empleadoId]
        );

        $data = [];
        foreach ($registros as $registro) {
            $data[$registro->x][$registro->serie] = floatval($registro->valor);
        }

        $xaxis = array_keys($data);
        $nombreseries = [];
        foreach ($data as $serie_data) {
            $nombreseries = array_unique(array_merge($nombreseries, array_keys($serie_data)));
        }

        $series = [];
        foreach ($nombreseries as $serie_nombre) {
            $serie_data = [];
            foreach ($xaxis as $x) {
                $serie_data[] = $data[$x][$serie_nombre] ?? 0;
            }
            $series[] = [
                'name' => $serie_nombre,
                'data' => $serie_data,
            ];
        }

        return [
            'chart' => [
                'type' => 'bar',
                'height' => 300,
                'stacked' => true,
            ],
            'series' => $series,
            'xaxis' => [
                'categories' =>  $xaxis,
                'labels' => [
                    'style' => [
                        'fontFamily' => 'inherit',
                    ],
                ],
            ],
            'yaxis' => [
                'title' => [

                ],
            ],
            'stroke' => [
                'curve' => 'smooth',
            ]
        ];
    }

    protected function extraJsOptions(): ?RawJs
    {
        return RawJs::make(<<<'JS'
        {
            yaxis: {
                labels: {
                    formatter: function (val, index) {
                        return new Intl.NumberFormat('es-ES', {
                            maximumFractionDigits: 0
                        }).format(val)
                    }
                }
            },
            dataLabels: {
                enabled: false,
            }
        }
        JS);
    }
}
