<?php

namespace App\Filament\Resources\BancoTipoMovimientoResource\Pages;

use App\Filament\Resources\BancoTipoMovimientoResource;
use Filament\Actions;
use Filament\Resources\Pages\EditRecord;

class EditBancoTipoMovimiento extends EditRecord
{
    protected static string $resource = BancoTipoMovimientoResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\ViewAction::make(),
            Actions\DeleteAction::make(),
        ];
    }

    protected function getRedirectUrl(): string	{
	    return $this->previousUrl ?? $this->getResource()::getUrl('index');
    }
}
