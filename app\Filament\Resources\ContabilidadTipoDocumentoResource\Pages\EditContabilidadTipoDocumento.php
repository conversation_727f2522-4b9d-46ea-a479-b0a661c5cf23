<?php

namespace App\Filament\Resources\ContabilidadTipoDocumentoResource\Pages;

use App\Filament\Resources\ContabilidadTipoDocumentoResource;
use Filament\Actions;
use Filament\Resources\Pages\EditRecord;

class EditContabilidadTipoDocumento extends EditRecord
{
    protected static string $resource = ContabilidadTipoDocumentoResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\ViewAction::make(),
            Actions\DeleteAction::make(),
        ];
    }

    protected function getRedirectUrl(): string	{
	    return $this->previousUrl ?? $this->getResource()::getUrl('index');
    }
}
