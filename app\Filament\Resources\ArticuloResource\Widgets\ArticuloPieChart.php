<?php

namespace App\Filament\Resources\ArticuloResource\Widgets;

use Filament\Support\RawJs;
use Illuminate\Support\Facades\DB;
use Leandrocfe\FilamentApexCharts\Widgets\ApexChartWidget;

class Articulo<PERSON>ie<PERSON>hart extends ApexChartWidget
{
    /**
     * Chart Id
     *
     * @var string
     */
    protected static ?string $chartId = 'articuloPieChart';

    /**
     * Widget Title
     *
     * @var string|null
     */
    protected static ?string $heading = 'Ventas por segmento';

    public $articuloId;

    /**
     * Chart options (series, labels, types, size, animations...)
     * https://apexcharts.com/docs/options
     *
     * @return array
     */

     protected function getOptions(): array
     {
        $empresaId = session()->get('empresa_id');
        $registros = DB::select(
            'SELECT s.nombre as segmento
	        , sum(l.importe) as importe
	        from lineas l
	        inner join facturas f on l.padre_id = f.id and l.padre_type =\'App\\\\Models\\\\Factura\'
            inner join personas p on f.persona_id = p.id
            left join segmentos s on p.segmento_id = s.id
	        where l.empresa_id = ? and l.articulo_id =? and f.tipo="venta"
	        group by 1',
            [$empresaId, $this->articuloId]
        );
        $labels = array_map(fn($row) => $row->segmento, $registros);
        $data = array_map(fn($row) => floatval($row->importe), $registros);

        return [
            'chart' => [
                'type' => 'pie',
                'height' => 350,
            ],
            'series' => $data,
            'labels' => $labels,
            'legend' => [
                'position' => 'bottom',
            ],
        ];
    }

    protected function extraJsOptions(): ?RawJs
    {
        return RawJs::make(<<<'JS'
        {
            yaxis: {
                labels: {
                    formatter: function (val, index) {
                        return new Intl.NumberFormat('es-ES', {
                            maximumFractionDigits: 0
                        }).format(val)
                    }
                }
            },
            dataLabels: {
                enabled: false,
            }
        }
        JS);
    }
}
