<?php

namespace App\Filament\Resources\ContabilidadAsignacionCuentaResource\Pages;

use App\Filament\Resources\ContabilidadAsignacionCuentaResource;
use Filament\Actions;
use Filament\Resources\Pages\CreateRecord;

class CreateContabilidadAsignacionCuenta extends CreateRecord
{
    protected static string $resource = ContabilidadAsignacionCuentaResource::class;

    protected function getRedirectUrl(): string
    {
        return $this->getResource()::getUrl('index');
    }

}


