<?php
namespace App\Enums;

use Filament\Support\Contracts\HasColor;

enum ProyectoEstado: string implements HasColor
{
    case Pendiente = 'Pendiente';
    case Cancelado = 'Cancelado';
    case En_Proceso = 'En Proceso';
    case Terminado = 'Terminado';
    case Desconocido = 'Desconocido';


    public function getColor(): string|array|null
    {
        return match($this) {
            self::Pendiente => 'danger',
            self::Cancelado => 'danger',
            self::En_Proceso => 'success',
            self::Terminado => 'gray',
            self::Desconocido => 'light',
        };
    }
}


