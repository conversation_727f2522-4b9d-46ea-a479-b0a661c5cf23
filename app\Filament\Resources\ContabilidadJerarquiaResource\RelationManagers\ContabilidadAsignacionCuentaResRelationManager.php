<?php

namespace App\Filament\Resources\ContabilidadJerarquiaResource\RelationManagers;

use App\Models\ContabilidadAsignacionCuentaRes;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\RelationManagers\RelationManager;
use Filament\Tables;
use Filament\Tables\Columns\Summarizers\Sum;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;
use Illuminate\Database\Eloquent\Model;

class ContabilidadAsignacionCuentaResRelationManager extends RelationManager
{
    protected static string $relationship = 'ContabilidadAsignacionCuentaRes';
    protected static ?string $title = 'Asignaciones a cta. res.';

    public function isReadOnly(): bool  {
        return false;
    }

    public function form(Form $form): Form
    {
        return $form
            ->schema(ContabilidadAsignacionCuentaRes::getForm($this->getOwnerRecord()->contabilidad_jerarquia_id));
    }

    public function table(Table $table): Table
    {
        return $table
            ->striped()
            ->heading('Asignaciones a cuentas de resultados')
            ->columns([
                TextColumn::make('centroCoste.nombre')
                    ->numeric()
                    ->sortable(),
                TextColumn::make('fecha_desde')
                    ->date('d-m-Y')
                    ->sortable()
                    ->label('Desde'),
                TextColumn::make('fecha_hasta')
                    ->date('d-m-Y')
                    ->sortable()
                    ->label('Hasta'),
                TextColumn::make('porcentaje')
                    ->numeric()
                    ->summarize(Sum::make())
                    ->sortable(),
            ])
            ->filters([
                //
            ])
            ->headerActions([
                Tables\Actions\CreateAction::make(),
            ])
            ->actions([
                Tables\Actions\EditAction::make()
                    ->label(false)
                    ->tooltip('Editar'),
                Tables\Actions\DeleteAction::make()
                    ->label(false)
                    ->tooltip('Borrar'),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }

    public static function canViewForRecord(Model $ownerRecord, string $pageClass): bool { return getPermission(static::class, 'canView', static::$relationship); }
    public function canViewAny(): bool {return getPermission(get_class(), __FUNCTION__);}
    public function canEdit($record): bool {return getPermission(get_class(), __FUNCTION__);}
    public function canCreate(): bool {return getPermission(get_class(), __FUNCTION__);}
    public function canDelete($record): bool {return getPermission(get_class(), __FUNCTION__);}
    public function canDeleteAny(): bool {return getPermission(get_class(), __FUNCTION__);}
}
