<?php

namespace App\Filament\Resources\FacturaResource\RelationManagers;

use App\Models\Linea;
use Filament\Forms\Form;
use Filament\Resources\RelationManagers\RelationManager;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Model;
use Livewire\Component;

class LineaRelationManager extends RelationManager
{
    protected static string $relationship = 'Linea';
    protected static ?string $title = 'Lineas de factura';
    public function isReadOnly(): bool { return false; }

    public function form(Form $form): Form
    {
        return $form
            ->schema(Linea::getForm($this->getOwnerRecord()->id, 'App\\Models\\Factura'));
    }

    public function table(Table $table): Table
    {
        return Linea::configureTable(
            $table,
            $this->getOwnerRecord()->id,
            'Factura',
            null,
            function (Table $table) {
                return $table
                    ->headerActions([
                        Tables\Actions\CreateAction::make()
                            ->after(function (Component $livewire) {
                                $livewire->dispatch('refreshFactura');
                            }),
                    ])
                    ->actions([
                        Tables\Actions\EditAction::make()
                            ->label(false)
                            ->tooltip('Editar')
                            ->after(function (Component $livewire) {
                                $livewire->dispatch('refreshFactura');
                            }),
                        Tables\Actions\DeleteAction::make()
                            ->label(false)
                            ->tooltip('Borrar')
                            ->after(function (Component $livewire) {
                                $livewire->dispatch('refreshFactura');
                            }),
                    ])
                    ->bulkActions([
                        Tables\Actions\BulkActionGroup::make([
                            Tables\Actions\DeleteBulkAction::make(),
                        ]),
                    ]);
            }
        )->filters([
            //
        ]);
    }

    public static function canViewForRecord(Model $ownerRecord, string $pageClass): bool { return getPermission(static::class, 'canView', static::$relationship); }
    public function canViewAny(): bool {return getPermission(get_class(), __FUNCTION__);}
    public function canEdit($record): bool {return getPermission(get_class(), __FUNCTION__);}
    public function canCreate(): bool {return getPermission(get_class(), __FUNCTION__);}
    public function canDelete($record): bool {return getPermission(get_class(), __FUNCTION__);}
    public function canDeleteAny(): bool {return getPermission(get_class(), __FUNCTION__);}
}
