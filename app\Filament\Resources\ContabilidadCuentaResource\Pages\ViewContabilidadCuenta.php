<?php

namespace App\Filament\Resources\ContabilidadCuentaResource\Pages;

use App\Filament\Resources\ContabilidadCuentaResource;
use App\Filament\Resources\ContabilidadCuentaResource\Widgets\CuentaImportesChart;
use App\Filament\Resources\ContabilidadCuentaResource\Widgets\CuentaSaldosChart;
use Filament\Actions;
use Filament\Resources\Pages\ViewRecord;

class ViewContabilidadCuenta extends ViewRecord
{
    protected static string $resource = ContabilidadCuentaResource::class;

    public function getTitle(): string  {
        return $this->record->cuenta_full;
    }

    protected function getHeaderActions(): array
    {
        return [
            Actions\Action::make('ver_lista')
                ->label('Ver Cuentas contables')
                ->icon('heroicon-s-arrow-left')
                ->color('secondary')
                ->url(fn () => url('/contabilidad-cuentas')),
            Actions\EditAction::make(),
        ];
    }

    protected function getHeaderWidgets():array
    {
        return [
            CuentaImportesChart::make(['cuentaId' => $this->record->id]),
            CuentaSaldosChart::make(['cuentaId' => $this->record->id]),
        ];
    }
}
