<?php

namespace App\Filament\Resources\ContabilidadApunteSplitResource\Pages;

use App\Filament\Resources\ContabilidadApunteSplitResource;
use Filament\Actions;
use Filament\Resources\Pages\ListRecords;

class ListContabilidadApunteSplits extends ListRecords
{
    protected static string $resource = ContabilidadApunteSplitResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\CreateAction::make(),
        ];
    }
}
