<?php

namespace App\Filament\Resources\ContabilidadJerarquiaResource\RelationManagers;

use App\Models\ContabilidadCuenta;
use Filament\Tables\Actions\CreateAction;
use Filament\Forms;
use Filament\Forms\Components\Select;
use Filament\Forms\Form;
use Filament\Notifications\Notification;
use Filament\Resources\RelationManagers\RelationManager;
use Filament\Tables;
use Filament\Tables\Actions\Action;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;
use Illuminate\Database\Eloquent\Model;


class ContabilidadCuentaRelationManager extends RelationManager
{
    protected static string $relationship = 'contabilidadCuenta';
    protected static ?string $title = 'Cuentas contables';

    public function isReadOnly(): bool {
        return false;
    }

    public function form(Form $form): Form
    {
        return $form
            ->schema(ContabilidadCuenta::getForm());
    }

    public function table(Table $table): Table
    {
        return $table
            ->striped()
            ->query(function (RelationManager $livewire) {
                return $livewire->ownerRecord->todasLasContabilidadCuentas();
            })
            ->columns([
                TextColumn::make('cuenta_id')
                    ->sortable()
                    ->searchable()
                    ->url(fn(ContabilidadCuenta $record) => "/contabilidad-cuentas/". $record->id),
                TextColumn::make('nombre')
                    ->sortable()
                    ->searchable()
                    ->url(fn(ContabilidadCuenta $record) => "/contabilidad-cuentas/". $record->id),
                TextColumn::make('masa')
                    ->sortable()
                    ->searchable(),
            ])
            ->filters([
                //
            ])
            ->headerActions(self::getActions())
            ->actions([
                Tables\Actions\EditAction::make()
                    ->label(false)
                    ->tooltip('Editar'),
                Tables\Actions\DeleteAction::make()
                    ->label(false)
                    ->tooltip('Borrar'),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }

    public static function getActions(): array
    {
        return [
            CreateAction::make(),
            Action::make('associate')
                ->label('Asociar cuenta') // Etiqueta del botón
                ->form([
                    Select::make('contabilidad_cuenta_id')
                        ->label('Seleccionar cuenta')
                        ->options(ContabilidadCuenta::query()->pluck('cuenta_full', 'id')) // Asume que 'nombre' es el campo que deseas mostrar
                        ->searchable() // Para facilitar la búsqueda en caso de muchos registros
                        ->required(),
                ])
                ->action(function (array $data, $livewire) {
                    $cuenta = ContabilidadCuenta::find($data['contabilidad_cuenta_id']); // Encuentra el registro seleccionado
                    if ($cuenta) {
                        // Obtener el registro de ContabilidadJerarquia actual
                        $jerarquia = $livewire->getOwnerRecord();

                        // Asignar la contabilidad_jerarquia_id del registro actual al registro seleccionado
                        $cuenta->contabilidad_jerarquia_id = $jerarquia->id;
                        $cuenta->save();

                        Notification::make()
                            ->title('Cuenta asociada correctamente.')
                            ->success()
                            ->send();
                    } else {
                        Notification::make()
                            ->title('No se pudo encontrar la cuenta.')
                            ->danger()
                            ->send();
                    }
                }),
        ];
    }

    public static function canViewForRecord(Model $ownerRecord, string $pageClass): bool { return getPermission(static::class, 'canView', "contabilidadCuenta"); }
    public function canViewAny(): bool {return getPermission(get_class(), __FUNCTION__);}
    public function canEdit($record): bool {return getPermission(get_class(), __FUNCTION__);}
    public function canCreate(): bool {return getPermission(get_class(), __FUNCTION__);}
    public function canDelete($record): bool {return getPermission(get_class(), __FUNCTION__);}
    public function canDeleteAny(): bool {return getPermission(get_class(), __FUNCTION__);}
}
