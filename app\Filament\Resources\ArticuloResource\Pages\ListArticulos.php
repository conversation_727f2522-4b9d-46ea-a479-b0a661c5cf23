<?php

namespace App\Filament\Resources\ArticuloResource\Pages;

use App\Filament\Resources\ArticuloResource;
use Filament\Actions;
use Filament\Resources\Pages\ListRecords;
use Filament\Resources\Components\Tab;

class ListArticulos extends ListRecords
{
    protected static string $resource = ArticuloResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\CreateAction::make(),
        ];
    }

    Public function getTabs(): array
    {
        return [
            'todos' => Tab::make('Todos los artículos'),
            'en_stock' => Tab::make('En stock')
                ->modifyQueryUsing(function ($query) {
                    return $query->where('stock', '>', 0);
                }),
            'pend_recibir' => Tab::make('Pend. recibir')
                ->modifyQueryUsing(function ($query) {
                    return $query->where('entradas_pendientes', '>', 0);
                }),
        ];
    }
}
