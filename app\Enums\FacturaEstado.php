<?php
namespace App\Enums;

enum FacturaEstado: string
{
    case Pendiente = 'Pendiente';
    case Pagada = 'Pagada';
    case Impagada = 'Impagada';
    case Cancelada = 'Cancelada';
    case Vencida = 'Vencida';


    public function getColor(): string
    {
        return match($this) {
            self::Cancelada => 'danger',
            self::Impagada => 'danger',
            self::Vencida => 'danger',
            self::Pendiente => 'primary',
            self::Pagada => 'success',
        };
    }
}


