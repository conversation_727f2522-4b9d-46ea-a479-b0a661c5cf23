<?php

namespace App\Filament\Pages;

use App\Enums\Prioridad;
use App\Enums\TareaEstado;
use App\Models\Empleado;
use App\Models\Tarea;
use Carbon\Carbon;
use Mokhosh\FilamentKanban\Pages\KanbanBoard;
use Illuminate\Database\Eloquent\Builder;
use Filament\Actions\CreateAction;
use Filament\Forms\Components\DatePicker;
use Filament\Forms\Components\Grid;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\Textarea;
use Filament\Forms\Components\TextInput;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Auth;
use Rupadana\FilamentSlider\Components\InputSlider;
use Rupadana\FilamentSlider\Components\InputSliderGroup;
use Illuminate\Support\Str;


class TareasKanbanBoard extends KanbanBoard
{
    protected static string $model = Tarea::class;
    protected static string $statusEnum = TareaEstado::class;
    protected static ?string $title = 'Mis Tareas';

    protected static string $headerView = 'tareas-kanban.kanban-header';
    protected static string $recordView = 'tareas-kanban.kanban-record';

    protected static string $recordTitleAttribute = 'nombre';
    protected static string $recordStatusAttribute = 'estado';

    protected string $editModalTitle = 'Editar Tarea';
    protected string $editModalSaveButtonLabel = 'Guardar';
    protected string $editModalCancelButtonLabel = 'Cancelar';
    protected string $editModalWidth = '2xl';

    public static function getNavigationLabel(): string {
        return 'Mis Tareas Kanban';
    }

    /*public static function getNavigationGroup(): ?string {
        return 'CRM';
    }*/

    protected function records(): Collection
    {
        $userId = Auth::user()->id;
        $empleadoId = Empleado::where('user_id', $userId)->value('id');

        return Tarea::where('a_empleado_id', $empleadoId)
            ->where(function ($query) {
                $query
                    ->whereNotIn('estado', [TareaEstado::Terminada, TareaEstado::Cancelada])
                    ->orWhere(function ($query) {
                        $query->where('estado', TareaEstado::Terminada)
                            ->where('fecha_cierre', '>=', Carbon::now()->subDays(7));
                    });
            })
            ->ordered()
            ->get();
    }
   

    protected function getEditModalFormSchema(string|int|null $recordId): array
    {
        $userId = Auth::user()->id;
        $empleadoId = Empleado::where('user_id', $userId)->value('id');

        return [
            Grid::make(2)
                ->schema([
                    TextInput::make('nombre')
                        ->required()
                        ->maxLength(50)
                        ->columnSpanFull()
                        ->label('Asunto'),
                    Textarea::make('descripcion')
                        ->columnSpanFull()
                        ->maxLength(250),
                    DatePicker::make('fecha_vencimiento'),
                    Select::make('prioridad')
                        ->required()
                        ->default ('Media')
                        ->options(Prioridad::class),
                    Select::make('a_empleado_id')
                        ->required()
                        ->relationship('aEmpleado', 'nombre')
                        ->default($empleadoId)
                        ->label('Asignado a'),
                    InputSliderGroup::make()
                        ->sliders([
                            InputSlider::make('progreso')->default(fn ($record) => $record?->progreso ?? 0),
                        ])
                        ->max(100)
                        ->min(0)
                        ->connect([
                            true,
                            false,
                        ]) 
                        ->label('Progreso'),
                    Select::make('estado')
                        ->required()
                        ->default ('No Iniciada')
                        ->options(TareaEstado::class),
                ])
        ];
    }

    public function onStatusChanged(string|int $recordId, string $status, array $fromOrderedIds, array $toOrderedIds): void
    {
        $tarea = Tarea::find($recordId);
        $tarea->cambiarEstado($status);
        //Tarea::setNewOrder($toOrderedIds);
    }

    protected function getHeaderActions(): array
    {
        return [
            CreateAction::make()
                ->model(Tarea::class)
                ->form(Tarea::getForm())
        ];
    }



}
