<?php

namespace App\Filament\Resources\AlmacenResource\Pages;

use App\Filament\Resources\AlmacenResource;
use Filament\Actions;
use Filament\Resources\Pages\ViewRecord;

class ViewAlmacen extends ViewRecord
{
    protected static string $resource = AlmacenResource::class;

    public function getTitle(): string  {
        return $this->record->nombre;
    }

    protected function getHeaderActions(): array
    {
        return [
            Actions\EditAction::make(),
        ];
    }
}
