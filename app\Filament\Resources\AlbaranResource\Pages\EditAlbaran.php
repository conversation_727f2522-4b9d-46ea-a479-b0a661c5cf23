<?php

namespace App\Filament\Resources\AlbaranResource\Pages;

use App\Filament\Resources\AlbaranResource;
use Filament\Actions;
use Filament\Resources\Pages\EditRecord;

class EditAlbaran extends EditRecord
{
    protected static string $resource = AlbaranResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\ViewAction::make(),
            Actions\DeleteAction::make(),
        ];
    }

    protected function getRedirectUrl(): string
	{
	    return $this->previousUrl ?? $this->getResource()::getUrl('index');
    }

}
