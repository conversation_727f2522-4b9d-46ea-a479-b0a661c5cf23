<?php

namespace App\Filament\Resources\ArticuloResource\RelationManagers;

use App\Models\Linea;
use Filament\Forms\Form;
use Filament\Resources\RelationManagers\RelationManager;
use Filament\Tables\Filters\Filter;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;

class LineaPedidoVentaRelationManager extends RelationManager
{
    protected static string $relationship = 'Linea';
    protected static ?string $title = 'Pedidos Venta';

    public function isReadOnly(): bool {
        return false;
    }

    public function form(Form $form): Form
    {
        return $form
            ->schema(Linea::getForm($this->getOwnerRecord()->id, 'App\\Models\\Pedido'));
    }

    public function table(Table $table): Table
    {
        return Linea::configureSimpleTable($table, null, 'Pedido', $this->getOwnerRecord()->id)
            ->filters([
                Filter::make('pedidos')
                    ->query(fn (Builder $query):
                        Builder => $query
                            ->where('padre_type', 'App\\Models\\Pedido')
                            ->whereHas('padre', function ($q) { $q->where('tipo', 'Venta'); })
                    )
                    ->default(),
            ])
            ->persistFiltersInSession();
    }

    public static function canViewForRecord(Model $ownerRecord, string $pageClass): bool { return getPermission(static::class, 'canView', static::$relationship); }
    public function canViewAny(): bool {return getPermission(get_class(), __FUNCTION__);}
    public function canEdit($record): bool {return getPermission(get_class(), __FUNCTION__);}
    public function canCreate(): bool {return getPermission(get_class(), __FUNCTION__);}
    public function canDelete($record): bool {return getPermission(get_class(), __FUNCTION__);}
    public function canDeleteAny(): bool {return getPermission(get_class(), __FUNCTION__);}
}
