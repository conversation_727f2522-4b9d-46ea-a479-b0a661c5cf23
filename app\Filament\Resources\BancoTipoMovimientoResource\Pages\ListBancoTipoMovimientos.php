<?php

namespace App\Filament\Resources\BancoTipoMovimientoResource\Pages;

use App\Filament\Resources\BancoTipoMovimientoResource;
use Filament\Actions;
use Filament\Resources\Pages\ListRecords;

class ListBancoTipoMovimientos extends ListRecords
{
    protected static string $resource = BancoTipoMovimientoResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\CreateAction::make(),
        ];
    }
}
