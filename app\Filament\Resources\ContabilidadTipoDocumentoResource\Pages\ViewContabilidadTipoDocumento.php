<?php

namespace App\Filament\Resources\ContabilidadTipoDocumentoResource\Pages;

use App\Filament\Resources\ContabilidadTipoDocumentoResource;
use Filament\Actions;
use Filament\Resources\Pages\ViewRecord;

class ViewContabilidadTipoDocumento extends ViewRecord
{
    protected static string $resource = ContabilidadTipoDocumentoResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\EditAction::make(),
        ];
    }
}
