<?php

namespace App\Filament\Resources\ContabilidadPresupuestoResource\Pages;

use App\Filament\Resources\ContabilidadPresupuestoResource;
use Filament\Actions;
use Filament\Resources\Pages\ViewRecord;

class ViewContabilidadPresupuesto extends ViewRecord
{
    protected static string $resource = ContabilidadPresupuestoResource::class;

    public function getTitle(): string  {
        return $this->record->ejercicio . ' - ' . $this->record->centroCoste->nombre . ' - ' . $this->record->contabilidadCuenta->cuenta_full;
    }

    protected function getHeaderActions(): array
    {
        return [
            Actions\Action::make('ver_lista')
                ->label('Ver Presupuestos')
                ->icon('heroicon-s-arrow-left')
                ->color('secondary')
                ->url(fn () => url('/contabilidad-presupuestos')),
            Actions\EditAction::make(),
        ];
    }
}
