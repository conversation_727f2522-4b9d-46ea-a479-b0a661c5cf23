<?php

namespace App\Filament\Resources\KpiResource\Widgets;

use Filament\Forms\Components\Select;
use Filament\Support\RawJs;
use Illuminate\Support\Facades\DB;
use Leandrocfe\FilamentApexCharts\Widgets\ApexChartWidget;

class KpiEvolucionChart extends ApexChartWidget
{
    protected static ?string $chartId = 'kpiEvolucionChart';
    protected static ?string $heading = 'Evolución';
    protected static ?string $pollingInterval = '120s';

    public $kpiId;
    public $selectedYear;
    public $defaultYear;

    protected function getFormSchema(): array
    {
        $empresaId = session()->get('empresa_id');
        $maxYear = DB::table('kpi_valors')
            ->selectRaw('MAX(YEAR(fecha)) as max_year')
            ->where('empresa_id', $empresaId)
            ->value('max_year');
        $this->defaultYear = DB::table('kpi_valors')
            ->selectRaw('MAX(YEAR(fecha)) as max_year')
            ->where('empresa_id', $empresaId)
            ->where('realizado_acumulado', '<>', '0')
            ->value('max_year');
        $minYear = DB::table('kpi_valors')
            ->selectRaw('MIN(YEAR(fecha)) as min_year')
            ->where('empresa_id', $empresaId)
            ->value('min_year');
        return [
            Select::make('selectedYear')
                ->label('Año')
                ->options(function () use ($minYear, $maxYear) {
                    $currentYear = date('Y');
                    return array_combine(
                        range($maxYear, $minYear),  // Rango descendente
                        range($maxYear, $minYear)
                    );
                })
                ->default($this->defaultYear) // Año por defecto es el actual
                ->live()
                ->afterStateUpdated(function () {
                    $this->getOptions();
                }),
        ];
    }

     protected function getOptions(): array
     {
        $selectedYear = $this->filterFormData? $this->filterFormData['selectedYear'] : $this->defaultYear;
        self::$heading = "Evolución " . $selectedYear;
        $registros = DB::select(
            'SELECT date_format(fecha,\'%Y-%m\') as fecha
            , if (zonaKPI(f.riesgo, f.objetivo, if(acumular=0, f.realizado, f.realizado_acumulado)) COLLATE utf8mb4_unicode_ci = \'Verde\',
                if(acumular=0, f.realizado, f.realizado_acumulado), null) as verde
	        , if (zonaKPI(f.riesgo, f.objetivo, if(acumular=0, f.realizado, f.realizado_acumulado)) COLLATE utf8mb4_unicode_ci = \'Amarillo\',
                if(acumular=0, f.realizado, f.realizado_acumulado), null) as amarillo
            , if (zonaKPI(f.riesgo, f.objetivo, if(acumular=0, f.realizado, f.realizado_acumulado)) COLLATE utf8mb4_unicode_ci = \'Rojo\',
                if(acumular=0, f.realizado, f.realizado_acumulado), null) as rojo
            , if (zonaKPI(f.riesgo, f.objetivo, if(acumular=0, f.realizado, f.realizado_acumulado)) COLLATE utf8mb4_unicode_ci = \'Gris\',
                if(acumular=0, f.realizado, f.realizado_acumulado), null) as gris
            , objetivo as umbral
            , riesgo
            , tendencia
            from kpi_valors f inner join kpis k on f.kpi_id = k.id
	        where f.padre_type=\'App\\\\Models\\\\Empresa\' and f.kpi_id = ? and year(f.fecha) = ?
            ORDER BY 1',
            [$this->kpiId, $selectedYear]
        );
        $fechas = array_map(fn($row) => $row->fecha, $registros);
        $verdeSeries = array_map(fn($row) => floatval($row->verde), $registros);
        $amarilloSeries = array_map(fn($row) => floatval($row->amarillo), $registros);
        $rojoSeries = array_map(fn($row) => floatval($row->rojo), $registros);
        $grisSeries = array_map(fn($row) => floatval($row->gris), $registros);
        $targetSeries = array_map(fn($row) => floatval($row->umbral), $registros);
        $riesgoSeries = array_map(fn($row) => floatval($row->riesgo), $registros);
        $tendenciaSeries = array_map(fn($row) => $row->tendencia, $registros);

        return [
            'chart' => [
                'type' => 'line',
                'height' => 290,
                'stacked' => true,
            ],
            'series' => [
                [
                    'name' => 'Verde',
                    'type' => 'column',
                    'stacked' => false,
                    'data' => $verdeSeries,
                ],
                [
                    'name' => 'Amarillo',
                    'type' => 'column',
                    'stacked' => false,
                    'data' => $amarilloSeries,
                ],
                [
                    'name' => 'Rojo',
                    'type' => 'column',
                    'stacked' => false,
                    'data' => $rojoSeries,
                ],
                [
                    'name' => 'Gris',
                    'type' => 'column',
                    'stacked' => false,
                    'data' => $grisSeries,
                ],
                [
                    'name' => 'Forecast',
                    'type' => 'column',
                    'stacked' => false,
                    'data' => $tendenciaSeries,
                ],
                [
                    'name' => 'Target',
                    'type' => 'area',
                    'stacked' => false,
                    'data' => $targetSeries,
                ],
                [
                    'name' => 'Riesgo',
                    'type' => 'area',
                    'stacked' => false,
                    'data' => $riesgoSeries,
                ],
            ],
            'colors'=> ['#008000', '#FFDE21', '#FF0000', '#777777', '#333', '#FFFD21', '#FF5555'],

            'xaxis' => [
                'categories' =>  $fechas,
                'type' => 'datetime',
                'labels' => [
                    'style' => [
                        'fontFamily' => 'inherit',
                    ],
                    'rotate' => -90, //no funciona con datetime
                ],
            ],
            'yaxis' => [
                'title' => [
                    'text' => '',
                ],
            ],
            'plotOptions' => [
                'bar' => [
                    'borderRadius' => 3,
                    'horizontal' => false,
                    'columnWidth' => '70%',
                ],
            ],
            'stroke' => [
                'width' => [0, 0, 0, 0, 2, 3, 3],
                'dashArray' => [0, 0, 0, 0, 3, 0, 0],
            ],
            'fill' => [
                'type' => ['solid', 'solid', 'solid', 'solid', 'gradient', 'gradient', 'gradient'],
                'gradient' => [
                    'shade' => 'light',
                    'type' => 'vertical',
                    'shadeIntensity' => 1,
                    'opacityFrom' => 0.6,
                    'opacityTo' => 0,
                    'stops' => [0, 70, 100]
                ],
            ],
            'legend' => [
                'show' => false,
            ],
        ];
    }

    protected function extraJsOptions(): ?RawJs
    {
        return RawJs::make(<<<'JS'
        {
            yaxis: {
                labels: {
                    formatter: function (val, index) {
                        return new Intl.NumberFormat('es-ES', {
                            maximumFractionDigits: 1
                        }).format(val)
                    }
                }
            },
            dataLabels: {
                enabled: false,
            }
        }
        JS);
    }
}
