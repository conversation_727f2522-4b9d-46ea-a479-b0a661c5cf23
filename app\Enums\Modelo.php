<?php

namespace App\Enums;

enum Modelo: string
{
    case SolicitudCompra = 'SolicitudCompra';
    case OrdenFabricacion = 'OrdenFabricacion';
    case FacturaCompra = 'FacturaCompra';
    case FacturaVenta = 'FacturaVenta';
    case AlbaranCompra = 'AlbaranCompra';
    case <PERSON>ranVenta = 'AlbaranVenta';
    case PedidoCompra = 'PedidoCompra';
    case PedidoVenta = 'PedidoVenta';
    case Proyecto = 'Proyecto';
    case ProyectoLinea = 'ProyectoLinea';
    case Presupuesto = 'Presupuesto';
    case PresupuestoGrupo = 'PresupuestoGrupo';
    case PresupuestoLinea = 'PresupuestoLinea';
}
