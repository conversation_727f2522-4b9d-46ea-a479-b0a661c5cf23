<?php

namespace App\Filament\Resources\AlbaranResource\RelationManagers;

use App\Models\Linea;
use Filament\Forms\Form;
use Filament\Resources\RelationManagers\RelationManager;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Model;

class LineaRelationManager extends RelationManager
{
    protected static string $relationship = 'Linea';
    protected static ?string $title = 'Lineas de albarán';
    public function isReadOnly(): bool { return false; }

    public function form(Form $form): Form
    {
        return $form
            ->schema(Linea::getForm($this->getOwnerRecord()->id, 'App\\Models\\Albaran'));
    }

    public function table(Table $table): Table
    {
        return Linea::configureSimpleTable($table, $this->getOwnerRecord()->id, 'Albarán')
            ->filters([
                //
            ]);
    }

    public static function canViewForRecord(Model $ownerRecord, string $pageClass): bool { return getPermission(static::class, 'canView', static::$relationship); }
    public function canViewAny(): bool {return getPermission(get_class(), __FUNCTION__);}
    public function canEdit($record): bool {return getPermission(get_class(), __FUNCTION__);}
    public function canCreate(): bool {return getPermission(get_class(), __FUNCTION__);}
    public function canDelete($record): bool {return getPermission(get_class(), __FUNCTION__);}
    public function canDeleteAny(): bool {return getPermission(get_class(), __FUNCTION__);}
}
