<?php

namespace App\Filament\Resources\KpiValorResource\Pages;

use App\Filament\Resources\KpiValorResource;
use Filament\Actions;
use Filament\Resources\Pages\EditRecord;

class EditKpiValor extends EditRecord
{
    protected static string $resource = KpiValorResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\ViewAction::make(),
            Actions\DeleteAction::make(),
        ];
    }

    protected function getRedirectUrl(): string	{
	    return $this->previousUrl ?? $this->getResource()::getUrl('index');
    }
}
