<?php

namespace App\Filament\Resources\ArticuloFamiliaResource\Pages;

use App\Filament\Resources\ArticuloFamiliaResource;
use App\Filament\Resources\ArticuloFamiliaResource\Widgets\FamiliaEvolucionChart;
use App\Filament\Resources\ArticuloFamiliaResource\Widgets\FamiliaPieChart;
use Filament\Actions;
use Filament\Resources\Pages\ViewRecord;

class ViewArticuloFamilia extends ViewRecord
{
    protected static string $resource = ArticuloFamiliaResource::class;

    public function getTitle(): string  {
        return $this->record->familia . ' / ' . $this->record->subfamilia;
    }

    protected function getHeaderActions(): array
    {
        return [
            Actions\EditAction::make(),
        ];
    }

    protected function getHeaderWidgets():array
    {
        return [
            FamiliaEvolucionChart::make(['familiaId' => $this->record->id]),
            FamiliaPieChart::make(['familiaId' => $this->record->id]),
        ];
    }
}
