<?php
namespace App\Enums;

use Filament\Support\Contracts\HasColor;
use Filament\Support\Contracts\HasLabel;
use Filament\Support\Contracts\HasIcon;

enum SolicitudCompraEstado: string implements HasColor, HasLabel
{
    case Abierta = 'Abierta';
    case Cerrada = 'Cerrada';
    case Urgente = 'Urgente';
    case Cancelada = 'Cancelada';


    public function getColor(): string|array|null
    {
        return match($this) {
            self::Cancelada => 'gray',
            self::Urgente => 'danger',
            self::Abierta => 'success',
            self::Cerrada => 'primary',
        };
    }

    public function getLabel(): string
    {
        return match ($this) {
            self::Cancelada => 'Cancelada',
            self::Urgente => 'Urgente',
            self::Abierta => 'Abierta',
            self::Cerrada => 'Cerrada',
        };
    }

}


