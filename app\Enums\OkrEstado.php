<?php
namespace App\Enums;

use Filament\Support\Contracts\HasColor;
use Filament\Support\Contracts\HasLabel;
use Filament\Support\Contracts\HasIcon;

enum OkrEstado: string implements HasColor, HasLabel, HasIcon
{
    case no_iniciado = 'No iniciado';
    case ok = 'Según lo planeado';
    case detras = 'Detrás';
    case en_riesgo = 'En riesgo';
    case cerrado = 'Cerrado';


    public function getColor(): string|array|null
    {
        return match($this) {
            self::no_iniciado => 'gray',
            self::ok => 'success',
            self::detras => 'warning',
            self::en_riesgo => 'danger',
            self::cerrado => 'primary',
        };
    }

    public function getLabel(): string
    {
        return match ($this) {
            self::no_iniciado => 'No Iniciado',
            self::ok => 'Según lo planeado',
            self::detras => 'Detrás',
            self::en_riesgo => 'En Riesgo',
            self::cerrado => 'Cerrado',
        };
    }

    public function getIcon(): ?string
    {
        return match ($this) {
            self::no_iniciado => 'heroicon-o-calendar-days',
            self::ok => 'heroicon-o-face-smile',
            self::detras => 'heroicon-o-face-frown',
            self::en_riesgo => 'heroicon-o-exclamation-triangle',
            self::cerrado => 'heroicon-o-x-circle',
        };
    }

}


