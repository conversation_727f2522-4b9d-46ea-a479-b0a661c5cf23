<?php

namespace App\Filament\Resources\HoraResource\Pages;

use App\Filament\Resources\HoraResource;
use Filament\Actions;
use Filament\Resources\Pages\EditRecord;

class EditHora extends EditRecord
{
    protected static string $resource = HoraResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\ViewAction::make(),
            Actions\DeleteAction::make(),
        ];
    }

    protected function getRedirectUrl(): string	{
	    return $this->previousUrl ?? $this->getResource()::getUrl('index');
    }
}
