<?php

namespace App\Filament\Resources\ContabilidadAsignacionCuentaResource\Pages;

use App\Filament\Resources\ContabilidadAsignacionCuentaResource;
use Filament\Actions;
use Filament\Resources\Pages\EditRecord;

class EditContabilidadAsignacionCuenta extends EditRecord
{
    protected static string $resource = ContabilidadAsignacionCuentaResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\ViewAction::make(),
            Actions\DeleteAction::make(),
        ];
    }

    protected function getRedirectUrl(): string	{
	    return $this->previousUrl ?? $this->getResource()::getUrl('index');
    }
}
