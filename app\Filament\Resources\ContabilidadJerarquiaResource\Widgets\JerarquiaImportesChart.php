<?php

namespace App\Filament\Resources\ContabilidadJerarquiaResource\Widgets;

use Filament\Forms\Components\Select;
use Filament\Support\RawJs;
use Illuminate\Support\Facades\DB;
use Leandrocfe\FilamentApexCharts\Widgets\ApexChartWidget;

class JerarquiaImportesChart extends ApexChartWidget
{
    protected static ?string $chartId = 'jerarquiaImportesChart';
    protected static ?string $heading = 'Importes';
    protected static ?string $pollingInterval = '120s';

    public $jerarquiaId;
    public $selectedYear;
    public $defaultYear;

    protected function getFormSchema(): array  {
        $empresaId = session()->get('empresa_id');
        $maxYear = DB::table('contabilidad_saldos')
            ->selectRaw('MAX(YEAR(fecha)) as max_year')
            ->where('empresa_id', $empresaId)
            ->value('max_year');
        $minYear = DB::table('contabilidad_saldos')
            ->selectRaw('MIN(YEAR(fecha)) as min_year')
            ->where('empresa_id', $empresaId)
            ->value('min_year');
        $this->defaultYear = $maxYear;
        return [
            Select::make('selectedYear')
                ->label('Año')
                ->options(function () use ($minYear, $maxYear) {
                    $currentYear = date('Y');
                    return array_combine(
                        range($maxYear, $minYear),  // Rango descendente
                        range($maxYear, $minYear)
                    );
                })
                ->default($this->defaultYear) // Año por defecto es el actual
                ->live()
                ->afterStateUpdated(function () {
                    $this->getOptions();
                }),
        ];
    }

     protected function getOptions(): array {
        $selectedYear = $this->filterFormData? $this->filterFormData['selectedYear'] : $this->defaultYear;
        self::$heading = "Importes " . $selectedYear;
        $registros = DB::select(
            'SELECT x.fecha,
                SUM(-x.debe_graph) as D,
                SUM(x.haber_graph) as H,
                SUM(x.apertura_graph) as A
            FROM contabilidad_jerarquias j
                left join contabilidad_jerarquias j2 on j.id = j2.contabilidad_jerarquia_id
                left join contabilidad_jerarquias j3 on j2.id = j3.contabilidad_jerarquia_id
                left JOIN contabilidad_cuentas c on (
	                j.id = c.contabilidad_jerarquia_id or j.id = c.pgc_jerarquia_id or
	                j2.id = c.contabilidad_jerarquia_id or j2.id = c.pgc_jerarquia_id or
	                j3.id = c.contabilidad_jerarquia_id or j3.id = c.pgc_jerarquia_id)
                left join contabilidad_saldos x on c.id = x.contabilidad_cuenta_id
            WHERE j.id = ? and not fecha is null   and year(x.fecha) = ?
            GROUP BY 1
            ORDER BY 1',
            [$this->jerarquiaId, $selectedYear]
        );

        $labels = array_map(fn($row) => $row->fecha, $registros);
        $dataD = array_map(fn($row) => $row->D, $registros);
        $dataH = array_map(fn($row) => $row->H, $registros);
        $dataA = array_map(fn($row) => $row->A, $registros);

          return [
             'chart' => [
                 'type' => 'bar',
                 'height' => 300,
                 'stacked' => true,
             ],
             'series' => [
                [
                    'name' => 'Apertura',
                    'data' =>  $dataA,
                ],
                [
                    'name' => 'Debe',
                    'data' =>  $dataD,
                ],
                [
                   'name' => 'Haber',
                   'data' =>  $dataH,
                ],
             ],
             'xaxis' => [
                 'categories' =>  $labels,
                 'type' => 'datetime',
                 'labels' => [
                     'style' => [
                         'fontFamily' => 'inherit',
                     ],
                 ],
             ],
             'yaxis' => [
                 'labels' => [
                     'style' => [
                         'fontFamily' => 'inherit',
                     ],
                 ],
            ],
            'colors' => ['#0077b6', '#f59e0b', '#FF5733'],
            'plotOptions' => [
                'bar' => [
                    'borderRadius' => 3,
                    'horizontal' => false,
                ],
            ],
        ];
    }

    protected function extraJsOptions(): ?RawJs
    {
        return RawJs::make(<<<'JS'
        {
            yaxis: {
                labels: {
                    formatter: function (val, index) {
                        return new Intl.NumberFormat('es-ES', {
                            maximumFractionDigits: 0
                        }).format(val)
                    }
                }
            },
            dataLabels: {
                enabled: false,
            }
        }
        JS);
    }
}
