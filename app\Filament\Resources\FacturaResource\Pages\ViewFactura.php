<?php

namespace App\Filament\Resources\FacturaResource\Pages;

use App\Filament\Resources\FacturaResource;
use Filament\Actions;
use Filament\Resources\Pages\ViewRecord;
use Livewire\Attributes\On;

class ViewFactura extends ViewRecord
{
    protected static string $resource = FacturaResource::class;

    public function getTitle(): string  {
        return 'Factura de ' . $this->record->tipo . ": " . $this->record->num;
    }

    #[On('refreshFactura')]
    
    public function refresh(): void
    {
        //funciona el refresco automático desde el presupuestolinearelationmananager
    }

    protected function getHeaderActions(): array
    {
        return [
            Actions\Action::make('ver_lista')
                ->label('Ver Facturas')
                ->icon('heroicon-s-arrow-left')
                ->color('secondary')
                ->url(fn () => url('/facturas')),

            Actions\EditAction::make(),
        ];
    }
}
