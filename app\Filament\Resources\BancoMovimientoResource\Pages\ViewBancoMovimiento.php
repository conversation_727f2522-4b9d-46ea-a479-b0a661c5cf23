<?php

namespace App\Filament\Resources\BancoMovimientoResource\Pages;

use App\Filament\Resources\BancoMovimientoResource;
use Filament\Actions;
use Filament\Resources\Pages\ViewRecord;

class ViewBancoMovimiento extends ViewRecord
{
    protected static string $resource = BancoMovimientoResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\Action::make('ver_lista')
                ->label('Ver Movimientos cuentas')
                ->icon('heroicon-s-arrow-left')
                ->color('secondary')
                ->url(fn () => url('/banco-movimientos')),
            Actions\EditAction::make(),
        ];
    }
}
