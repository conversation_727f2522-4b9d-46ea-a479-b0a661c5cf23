<?php

namespace App\Policies;

use App\Models\User;
use App\Models\Serie;
use Illuminate\Auth\Access\HandlesAuthorization;

class SeriePolicy
{
    use HandlesAuthorization;

    /**
     * Determine whether the user can view any models.
     */
    public function viewAny(User $user): bool
    {
        return $user->can('view_any_serie');
    }

    /**
     * Determine whether the user can view the model.
     */
    public function view(User $user, Serie $serie): bool
    {
        return $user->can('view_serie');
    }

    /**
     * Determine whether the user can create models.
     */
    public function create(User $user): bool
    {
        return $user->can('create_serie');
    }

    /**
     * Determine whether the user can update the model.
     */
    public function update(User $user, Serie $serie): bool
    {
        return $user->can('update_serie');
    }

    /**
     * Determine whether the user can delete the model.
     */
    public function delete(User $user, Serie $serie): bool
    {
        return $user->can('delete_serie');
    }

    /**
     * Determine whether the user can bulk delete.
     */
    public function deleteAny(User $user): bool
    {
        return $user->can('delete_any_serie');
    }

    /**
     * Determine whether the user can permanently delete.
     */
    public function forceDelete(User $user, Serie $serie): bool
    {
        return $user->can('force_delete_serie');
    }

    /**
     * Determine whether the user can permanently bulk delete.
     */
    public function forceDeleteAny(User $user): bool
    {
        return $user->can('force_delete_any_serie');
    }

    /**
     * Determine whether the user can restore.
     */
    public function restore(User $user, Serie $serie): bool
    {
        return $user->can('restore_serie');
    }

    /**
     * Determine whether the user can bulk restore.
     */
    public function restoreAny(User $user): bool
    {
        return $user->can('restore_any_serie');
    }

    /**
     * Determine whether the user can replicate.
     */
    public function replicate(User $user, Serie $serie): bool
    {
        return $user->can('replicate_serie');
    }

    /**
     * Determine whether the user can reorder.
     */
    public function reorder(User $user): bool
    {
        return $user->can('reorder_serie');
    }
}
