<?php

namespace App\Filament\Resources\UserResource\RelationManagers;

use App\Models\EquipoUsuario;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\RelationManagers\RelationManager;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;

class EquipoUsuarioRelationManager extends RelationManager
{
    protected static string $relationship = 'EquipoUsuario';
    protected static ?string $title = 'Miembro de';

    public function form(Form $form): Form
    {
        return $form
            ->schema(EquipoUsuario::getForm(null, $this->getOwnerRecord()->id));
    }

    public function table(Table $table): Table
    {
        return $table
            ->columns(EquipoUsuario::getTabla(null, $this->getOwnerRecord()->id))
            ->filters([
                //
            ])
            ->headerActions([
                Tables\Actions\CreateAction::make(),
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
                Tables\Actions\DeleteAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }
}
