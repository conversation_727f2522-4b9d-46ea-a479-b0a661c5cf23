<?php

namespace App\Filament\Resources\KpiResource\Widgets;

use App\Models\Kpi;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\TextInput;
use Filament\Support\RawJs;
use Illuminate\Support\Facades\DB;
use Leandrocfe\FilamentApexCharts\Widgets\ApexChartWidget;

class KpiGaugeChart extends ApexChartWidget
{
    protected static ?string $chartId = 'KpiGaugeChart';
    protected static ?string $heading = 'Consecución';
    protected static ?string $pollingInterval = '120s';
    public $kpiId;

    protected function getOptions(): array
    {
        $registros = DB::select(
            'SELECT round(v.cumplimiento*100,0) as valor, v.realizado_acumulado  as realizado
             FROM kpi_valors v
             WHERE v.kpi_id = ? and v.padre_type=\'App\\\\Models\\\\Empresa\' and not v.cumplimiento is null
             order by v.fecha desc limit 1',
            [$this->kpiId]
        );

        $valorgraf = $registros[0]->valor ?? 0;
        $valor = $registros[0]->realizado ?? 0;

        return [
            'chart' => [
                'type' => 'radialBar',
                'height' => 320,
            ],
            'series' => [$valorgraf],
            'plotOptions' => [
                'radialBar' => [
                    'startAngle' => -135,
                    'endAngle' => 135,
                    'dataLabels' => [
                        'show' => true,
                        'name' => [
                            'show' => false,
                            'color' => '#9ca3af',
                            'fontWeight' => 600,
                        ],
                        'value' => [
                            'show' => true,
                            'color' => '#9ca3af',
                            'fontWeight' => 600,
                            'fontSize' => '20px',
                        ],
                    ],

                ],
            ],
            'stroke' => [
                'dashArray' => 4,
            ],
            'fill' => [
                'type' => 'gradient',
                'gradient' => [
                    //'shade' => 'dark',
                    'type' => 'horizontal',
                    'gradientToColors' => ['#00ff06'],
                    'stops' => [0,100],
                    //'colorStops' => [],
                    //'opacityFrom' => 0.85,
                    //'opacityTo' => 0.15,
                ],
            ],
            'labels' => [round($valor,2)],
            'colors' => ['#FF00f9'],
        ];
    }


    protected function extraJsOptions(): ?RawJs
    {
        return RawJs::make(<<<'JS'
        {
            chartOptions: {
            chart: {
              height: 350,
              type: 'radialBar',
              offsetY: -10
            },
            plotOptions: {
              radialBar: {
                startAngle: -135,
                endAngle: 135,
                dataLabels: {
                  name: {
                    fontSize: '16px',
                    color: undefined,
                    offsetY: 120
                  },
                  value: {
                    offsetY: 76,
                    fontSize: '22px',
                    color: undefined,
                    formatter: function (val) {
                      return val + "%";
                    }
                  }
                }
              }
            },
            fill: {
              type: 'gradient',
              gradient: {
                  shade: 'dark',
                  shadeIntensity: 0.15,
                  inverseColors: false,
                  opacityFrom: 1,
                  opacityTo: 1,
                  stops: [0, 50, 65, 91]
              },
            },
            stroke: {
              dashArray: 4
            },
            labels: ['Median Ratio'],
          },
        }
        JS);
    }


}
