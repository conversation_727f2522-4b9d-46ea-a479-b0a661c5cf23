<?php

namespace App\Filament\Resources\KpiValorResource\Pages;

use App\Filament\Resources\KpiValorResource;
use Filament\Actions;
use Filament\Resources\Pages\ViewRecord;

class ViewKpiValor extends ViewRecord
{
    protected static string $resource = KpiValorResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\EditAction::make(),
        ];
    }
}
