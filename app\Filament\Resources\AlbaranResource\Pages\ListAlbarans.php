<?php

namespace App\Filament\Resources\AlbaranResource\Pages;

use App\Filament\Resources\AlbaranResource;
use Filament\Actions;
use Filament\Resources\Pages\ListRecords;
use Filament\Resources\Components\Tab;

class ListAlbarans extends ListRecords
{
    protected static string $resource = AlbaranResource::class;

    Public function getTabs(): array
    {
        return [
            'venta' => Tab::make('Venta')
                ->modifyQueryUsing(function ($query) {
                    return $query->where('tipo', 'Venta');
                }),
            'compra' => Tab::make('Compra')
                ->modifyQueryUsing(function ($query) {
                    return $query->where('tipo', 'Compra');
                }),
            'vpendientes' => Tab::make('Venta Pendientes')
                ->modifyQueryUsing(function ($query) {
                    return $query->where('estado', 'Pendiente')->where('tipo', 'Venta');
                }),
            'cpendientes' => Tab::make('Compra pendientes')
                ->modifyQueryUsing(function ($query) {
                    return $query->where('estado', 'Pendiente')->where('tipo', 'Compra');
                }),
        ];
    }

    protected function getHeaderActions(): array
    {
        return [
            Actions\CreateAction::make(),
        ];
    }
}
