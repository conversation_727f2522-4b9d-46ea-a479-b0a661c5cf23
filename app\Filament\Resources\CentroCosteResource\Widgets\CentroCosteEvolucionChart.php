<?php

namespace App\Filament\Resources\CentroCosteResource\Widgets;

use Filament\Support\RawJs;
use Illuminate\Support\Facades\DB;
use Leandrocfe\FilamentApexCharts\Widgets\ApexChartWidget;

class CentroCosteEvolucionChart extends ApexChartWidget
{
    protected static ?string $chartId = 'centrocosteEvolucionChart';
    protected static ?string $heading = 'Margen';
    protected static ?string $pollingInterval = '120s';

    public $centroCosteId;

     protected function getOptions(): array
     {
        $registros = DB::select(
            'select concat(year(x.fecha),\'-\', QUARTER(x.fecha)) as fecha
            , coalesce(ccp.nombre, cc.nombre, "No asignado") as centrocoste
            , sum(-x.importe) as importe
            from contabilidad_apuntes_extended x
            inner join contabilidad_cuentas c on x.contabilidad_cuenta_id = c.id
            left join centro_costes cc on x.centro_coste_id = cc.id
            left join centro_costes ccp on cc.centro_coste_id = ccp.id
            where c.masa in (\'Ingresos\', \'Gastos\')
            and (cc.id = ? or cc.centro_coste_id = ?)
            group by 1,2
            order by 1,2',
            [$this->centroCosteId,$this->centroCosteId]
        );
        $data = [];
        foreach ($registros as $registro) {
            $data[$registro->fecha][$registro->centrocoste] = floatval($registro->importe);
        }

        $fechas = array_keys($data);
        $centroscoste = [];
        foreach ($data as $centroscoste_data) {
            $centroscoste = array_unique(array_merge($centroscoste, array_keys($centroscoste_data)));
        }

        $series = [];
        foreach ($centroscoste as $centrocoste) {
            $serie_data = [];
            foreach ($fechas as $fecha) {
                $serie_data[] = $data[$fecha][$centrocoste] ?? 0;
            }
            $series[] = [
                'name' => $centrocoste,
                'data' => $serie_data,
            ];
        }

        return [
            'chart' => [
                'type' => 'line',
                'height' => 300,
                'stacked' => false,
            ],
            'series' => $series,
            'xaxis' => [
                'categories' =>  $fechas,
                'labels' => [
                    'style' => [
                        'fontFamily' => 'inherit',
                    ],
                ],
            ],
            'yaxis' => [
                'title' => [

                ],
            ],
            'stroke' => [
                'curve' => 'smooth',
            ]
        ];
    }

    protected function extraJsOptions(): ?RawJs
    {
        return RawJs::make(<<<'JS'
        {
            yaxis: {
                labels: {
                    formatter: function (val, index) {
                        return new Intl.NumberFormat('es-ES', {
                            maximumFractionDigits: 0
                        }).format(val)
                    }
                }
            },
            dataLabels: {
                enabled: false,
            }
        }
        JS);
    }
}
