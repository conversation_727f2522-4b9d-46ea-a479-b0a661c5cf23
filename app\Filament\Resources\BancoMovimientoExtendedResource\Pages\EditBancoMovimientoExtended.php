<?php

namespace App\Filament\Resources\BancoMovimientoExtendedResource\Pages;

use App\Filament\Resources\BancoMovimientoExtendedResource;
use Filament\Actions;
use Filament\Resources\Pages\EditRecord;

class EditBancoMovimientoExtended extends EditRecord
{
    protected static string $resource = BancoMovimientoExtendedResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\ViewAction::make(),
            Actions\DeleteAction::make(),
        ];
    }

    protected function getRedirectUrl(): string	{
	    return $this->previousUrl ?? $this->getResource()::getUrl('index');
    }
}
