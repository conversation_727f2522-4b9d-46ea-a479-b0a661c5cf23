<?php

namespace App\Filament\Resources\BancoMovimientoSplitResource\Pages;

use App\Filament\Resources\BancoMovimientoSplitResource;
use Filament\Actions;
use Filament\Resources\Pages\ListRecords;

class ListBancoMovimientoSplits extends ListRecords
{
    protected static string $resource = BancoMovimientoSplitResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\CreateAction::make(),
        ];
    }
}
