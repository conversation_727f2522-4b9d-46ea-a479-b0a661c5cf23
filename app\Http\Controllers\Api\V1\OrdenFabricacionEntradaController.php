<?php

namespace App\Http\Controllers\Api\V1;

//use App\Http\Filters\V1\OrdenFabricacionEntradaFilter;
use App\Http\Resources\V1\OrdenFabricacionEntradaApiResource;
use App\Models\OrdenFabricacionEntrada;
use App\Policies\V1\OrdenFabricacionPolicy;

class OrdenFabricacionEntradaController extends BaseApiController
{
    protected $policyClass = OrdenFabricacionPolicy::class;
    protected $modelClass = OrdenFabricacionEntrada::class;
    protected $resourceClass = OrdenFabricacionEntradaApiResource::class;
    protected $filterClass = null;
    protected $withRelations = [];
}

