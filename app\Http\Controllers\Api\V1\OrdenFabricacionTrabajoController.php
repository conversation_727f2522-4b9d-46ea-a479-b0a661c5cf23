<?php

namespace App\Http\Controllers\Api\V1;

//use App\Http\Filters\V1\OrdenFabricacionTrabajoFilter;
use App\Http\Resources\V1\OrdenFabricacionTrabajoApiResource;
use App\Models\OrdenFabricacionTrabajo;
use App\Policies\V1\OrdenFabricacionPolicy;

class OrdenFabricacionTrabajoController extends BaseApiController
{
    protected $policyClass = OrdenFabricacionPolicy::class;
    protected $modelClass = OrdenFabricacionTrabajo::class;
    protected $resourceClass = OrdenFabricacionTrabajoApiResource::class;
    protected $filterClass = null;
    protected $withRelations = [];
}

