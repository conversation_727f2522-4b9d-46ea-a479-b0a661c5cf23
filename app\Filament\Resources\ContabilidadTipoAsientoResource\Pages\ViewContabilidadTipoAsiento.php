<?php

namespace App\Filament\Resources\ContabilidadTipoAsientoResource\Pages;

use App\Filament\Resources\ContabilidadTipoAsientoResource;
use Filament\Actions;
use Filament\Resources\Pages\ViewRecord;

class ViewContabilidadTipoAsiento extends ViewRecord
{
    protected static string $resource = ContabilidadTipoAsientoResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\EditAction::make()
                ->label(false)
                ->tooltip('Editar'),
        ];
    }
}
