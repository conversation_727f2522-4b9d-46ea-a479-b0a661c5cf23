<?php

namespace App\Filament\Resources\ConexionResource\Pages;

use App\Filament\Resources\ConexionResource;
use Filament\Actions;
use Filament\Resources\Pages\ViewRecord;

class ViewConexion extends ViewRecord
{
    protected static string $resource = ConexionResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\EditAction::make(),
        ];
    }
}
