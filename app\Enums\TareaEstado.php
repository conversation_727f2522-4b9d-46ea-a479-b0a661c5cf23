<?php
namespace App\Enums;

use Filament\Support\Contracts\HasColor;
use Filament\Support\Contracts\HasLabel;
use Mokhosh\FilamentKanban\Concerns\IsKanbanStatus;

enum TareaEstado: string implements HasColor, HasLabel
{
    use IsKanbanStatus;

    case No_Iniciada = 'No Iniciada';
    case Pendiente = 'Pendiente';
    case Cancelada = 'Cancelada';
    case En_Curso = 'En Curso';
    case Terminada = 'Terminada';


    public function getColor(): string|array|null
    {
        return match($this) {
            self::No_Iniciada => 'danger',
            self::Pendiente => 'warning',
            self::Cancelada => 'gray',
            self::En_Curso => 'primary',
            self::Terminada => 'success',
        };
    }

    public function getLabel(): string
    {
        return $this->value;
    }

    public function estaViva(): bool    {
        return in_array($this, [self::No_Iniciada, self::Pendiente, self::En_Curso]);
    }

    public static function kanbanCases(): array
{
    return [
        static::No_Iniciada,
        static::En_Curso,
        static::Pendiente,
        static::Terminada,
    ];
}

}


