<?php

namespace App\Models;

use App\Traits\BelongsToEmpresa;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Support\Facades\Auth;
use Illuminate\Database\Eloquent\Builder;
use App\Http\Filters\V1\QueryFilter;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\TextInput;
use Filament\Tables\Columns\TextColumn;

class OrdenFabricacionTrabajo extends Model
{
    use BelongsToEmpresa;
    use HasFactory;

    protected $table = 'ordenes_fabricacion_trabajos';

    protected $casts = [
        'id' => 'integer',
        'empresa_id' => 'integer',
        'orden_fabricacion_id' => 'integer',
        'horas' => 'float',
        'hora_tipo_id' => 'integer',
        'created_by' => 'integer',
        'updated_by' => 'integer',
    ];

    protected static function booted()  {
        static::creating(function ($model) {
            $model->created_by = Auth::id();
            $model->updated_by = Auth::id();
        });

        static::updating(function ($model) {
            $model->updated_by = Auth::id();
        });
    }

    public function scopeFilter(Builder $builder, QueryFilter $filters){
        return $filters->apply($builder);
    }

    public static function getForm($ordenFabricacionId = null) :array
    {
        return [
            Select::make('orden_fabricacion_id')
                ->relationship('ordenFabricacion', 'id')
                ->getOptionLabelFromRecordUsing(fn ($record) =>
                    $record->codigo
                        ? "{$record->codigo} {$record->nombre}"
                        : $record->nombre
                )
                ->required()
                ->preload()
                ->searchable()
                ->default($ordenFabricacionId)
                ->label('Orden de fabricación')
                ->hidden(function () use ($ordenFabricacionId){return $ordenFabricacionId !== null;}),
            TextInput::make('horas')
                ->required()
                ->numeric(),
            Select::make('hora_tipo_id')
                ->required()
                ->relationship('horaTipo', 'nombre'),
            TextInput::make('descripcion')
                ->maxLength(200),
        ];
    }

    public static function getTabla($ordenFabricacionId = null) :array
    {
        return [
            TextColumn::make('ordenFabricacion.nombre')
                ->label('Orden de fabricación')
                ->sortable()
                ->formatStateUsing(function ($state, $record) {
                    return $record->ordenFabricacion->nombreCompleto();
                })
                ->url(fn(Model $record) => "/ordenes-fabricacion/".$record->orden_fabricacion_id)
                ->hidden(function () use ($ordenFabricacionId){return $ordenFabricacionId !== null;}),
            TextColumn::make('horaTipo.nombre')
                ->label('Tipo')
                ->sortable(),
            TextColumn::make('horas')
                ->label('Horas')
                ->sortable(),
            TextColumn::make('descripcion')
                ->label('Descripción')
                ->sortable(),
            TextColumn::make('empresa.nombre')
                ->toggleable(isToggledHiddenByDefault: true),
            TextColumn::make('created_at')
                ->dateTime()
                ->sortable()
                ->toggleable(isToggledHiddenByDefault: true),
            TextColumn::make('updated_at')
                ->dateTime()
                ->sortable()
                ->toggleable(isToggledHiddenByDefault: true),
        ];
    }

    public function empresa(): BelongsTo {
        return $this->belongsTo(Empresa::class);
    }

    public function ordenFabricacion(): BelongsTo {
        return $this->belongsTo(OrdenFabricacion::class);
    }

    public function horaTipo(): BelongsTo {
        return $this->belongsTo(HoraTipo::class);
    }

    public function creator() {
        return $this->belongsTo(User::class, 'created_by');
    }

    public function updater() {
        return $this->belongsTo(User::class, 'updated_by');
    }
}
