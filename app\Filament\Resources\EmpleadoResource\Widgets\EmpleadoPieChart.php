<?php

namespace App\Filament\Resources\EmpleadoResource\Widgets;

use Filament\Support\RawJs;
use Illuminate\Support\Facades\DB;
use Leandrocfe\FilamentApexCharts\Widgets\ApexChartWidget;

class EmpleadoPie<PERSON>hart extends ApexChartWidget
{
    protected static ?string $chartId = 'empleadoPieChart';
    protected static ?string $heading = 'Ventas por segmento';

    public $empleadoId;

     protected function getOptions(): array
     {
        $registros = DB::select(
            'SELECT s.nombre as serie
                , sum(x.facturado) as valor
            from contabilidad_apuntes_extended x
            inner join contabilidad_cuentas c on x.contabilidad_cuenta_id = c.id
            inner join personas p on x.persona_id = p.id
            left join segmentos s on p.segmento_id = s.id
            where facturado <> 0 and p.empleado_id =? AND x.fecha >= DATE_SUB(CURDATE(), INTERVAL 24 MONTH)
            GROUP BY 1
            ORDER BY 1',
            [$this->empleadoId]
        );
        $labels = array_map(fn($row) => $row->serie, $registros);
        $data = array_map(fn($row) => floatval($row->valor), $registros);

        return [
            'chart' => [
                'type' => 'pie',
                'height' => 350,
            ],
            'series' => $data,
            'labels' => $labels,
            'legend' => [
                'position' => 'bottom',
            ],
        ];
    }

    protected function extraJsOptions(): ?RawJs
    {
        return RawJs::make(<<<'JS'
        {
            yaxis: {
                labels: {
                    formatter: function (val, index) {
                        return new Intl.NumberFormat('es-ES', {
                            maximumFractionDigits: 0
                        }).format(val)
                    }
                }
            },
            dataLabels: {
                enabled: false,
            }
        }
        JS);
    }
}
