<?php

namespace App\Filament\Resources\ContabilidadApunteExtendedResource\Pages;

use App\Filament\Resources\ContabilidadApunteExtendedResource;
use Filament\Actions;
use Filament\Resources\Pages\ListRecords;

class ListContabilidadApunteExtendeds extends ListRecords
{
    protected static string $resource = ContabilidadApunteExtendedResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\CreateAction::make(),
        ];
    }
}
