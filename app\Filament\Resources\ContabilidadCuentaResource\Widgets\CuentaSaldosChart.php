<?php

namespace App\Filament\Resources\ContabilidadCuentaResource\Widgets;

use Filament\Support\RawJs;
use Illuminate\Support\Facades\DB;
use Leandrocfe\FilamentApexCharts\Widgets\ApexChartWidget;

class <PERSON>uentaSaldosChart extends ApexChartWidget
{
    protected static ?string $chartId = 'cuentaSaldosChart';
    protected static ?string $heading = 'Saldos';
    protected static ?string $pollingInterval = '120s';

    public $cuentaId;

    /**
     * Chart options (series, labels, types, size, animations...)
     * https://apexcharts.com/docs/options
     *
     * @return array
     */

     protected function getOptions(): array
     {
        $registros = DB::select(
            'SELECT f.mes_abreviacion as x,
                f.mes_numero,
                f.ejercicio as serie,
                SUM(if(c.masa=\'Ingresos\' or c.masa=\'Pasivo\', -s.saldo, s.saldo)) as valor
             FROM contabilidad_saldos s
             JOIN contabilidad_cuentas c on s.contabilidad_cuenta_id = c.id
             JOIN fechas f on s.fecha = f.fecha
             WHERE s.contabilidad_cuenta_id = ?
             GROUP BY 1,2,3
             ORDER BY 2,3',
            [$this->cuentaId]
        );

        $data = [];
        foreach ($registros as $registro) {
            $data[$registro->x][$registro->serie] = floatval($registro->valor);
        }

        $xaxis = array_keys($data);
        $nombreseries = [];
        foreach ($data as $serie_data) {
            $nombreseries = array_unique(array_merge($nombreseries, array_keys($serie_data)));
        }

        $series = [];
        foreach ($nombreseries as $serie_nombre) {
            $serie_data = [];
            foreach ($xaxis as $x) {
                $serie_data[] = $data[$x][$serie_nombre] ?? null;
            }
            $series[] = [
                'name' => $serie_nombre,
                'data' => $serie_data,
            ];
        }

        return [
            'chart' => [
                'type' => 'line',
                'height' => 300,
                'stacked' => false,
            ],
            'series' => $series,
            'xaxis' => [
                'categories' =>  $xaxis,
                'labels' => [
                    'style' => [
                        'fontFamily' => 'inherit',
                    ],
                ],
            ],
            'yaxis' => [
                'title' => [

                ],
            ],
            'stroke' => [
                'curve' => 'smooth',
            ]
        ];
    }

    protected function extraJsOptions(): ?RawJs
    {
        return RawJs::make(<<<'JS'
        {
            yaxis: {
                labels: {
                    formatter: function (val, index) {
                        return new Intl.NumberFormat('es-ES', {
                            maximumFractionDigits: 0
                        }).format(val)
                    }
                }
            },
            dataLabels: {
                enabled: false,
            }
        }
        JS);
    }
}
