<?php

namespace App\Filament\Resources\EmpleadoResource\RelationManagers;

use App\Models\Hora;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\RelationManagers\RelationManager;
use Filament\Tables;
use Filament\Tables\Columns\Summarizers\Sum;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;
use Illuminate\Database\Eloquent\Model;

class HoraRelationManager extends RelationManager
{
    protected static string $relationship = 'Hora';
    protected static ?string $title = 'Horas';

    public function isReadOnly(): bool  {
        return false;
    }

    public function form(Form $form): Form  {
        return $form
        ->schema(Hora::getForm($this->getOwnerRecord()->id, null));
    }

    public function table(Table $table): Table  {
        return $table
            ->striped()
            ->columns([
                TextColumn::make('proyecto.nombre')
                    ->sortable()
                    ->url(fn(Hora $record) => "/proyectos/". $record->proyecto_id),
                TextColumn::make('inicio')
                    ->dateTime('d-m-Y')
                    ->sortable(),
                TextColumn::make('fin')
                    ->dateTime('d-m-Y')
                    ->sortable(),
                TextColumn::make('horas')
                    ->numeric()
                    ->sortable()
                    ->summarize(Sum::make()),
                TextColumn::make('descripcion')
                    ->searchable(),
                TextColumn::make('horaTipo.nombre')
                    ->sortable()
                    ->label('Tipo'),
                TextColumn::make('created_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
                TextColumn::make('updated_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                //
            ])
            ->headerActions([
                Tables\Actions\CreateAction::make(),
            ])
            ->actions([
                Tables\Actions\EditAction::make()
                    ->label(false)
                    ->tooltip('Editar'),
                Tables\Actions\DeleteAction::make()
                    ->label(false)
                    ->tooltip('Borrar'),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }

    public static function canViewForRecord(Model $ownerRecord, string $pageClass): bool { return getPermission(static::class, 'canView', static::$relationship); }
    public function canViewAny(): bool {return getPermission(get_class(), __FUNCTION__);}
    public function canEdit($record): bool {return getPermission(get_class(), __FUNCTION__);}
    public function canCreate(): bool {return getPermission(get_class(), __FUNCTION__);}
    public function canDelete($record): bool {return getPermission(get_class(), __FUNCTION__);}
    public function canDeleteAny(): bool {return getPermission(get_class(), __FUNCTION__);}
}
