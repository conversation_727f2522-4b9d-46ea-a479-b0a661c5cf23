<?php

namespace App\Filament\Resources\BancoTipoMovimientoResource\Pages;

use App\Filament\Resources\BancoTipoMovimientoResource;
use Filament\Actions;
use Filament\Resources\Pages\ViewRecord;

class ViewBancoTipoMovimiento extends ViewRecord
{
    protected static string $resource = BancoTipoMovimientoResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\EditAction::make(),
        ];
    }
}
