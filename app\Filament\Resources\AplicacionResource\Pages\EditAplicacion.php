<?php

namespace App\Filament\Resources\AplicacionResource\Pages;

use App\Filament\Resources\AplicacionResource;
use Filament\Actions;
use Filament\Resources\Pages\EditRecord;

class EditAplicacion extends EditRecord
{
    protected static string $resource = AplicacionResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\ViewAction::make(),
            Actions\DeleteAction::make(),
        ];
    }

    protected function getRedirectUrl(): string
	{
	    return $this->previousUrl ?? $this->getResource()::getUrl('index');
    }
}
