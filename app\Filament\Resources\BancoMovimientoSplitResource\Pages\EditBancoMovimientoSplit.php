<?php

namespace App\Filament\Resources\BancoMovimientoSplitResource\Pages;

use App\Filament\Resources\BancoMovimientoSplitResource;
use Filament\Actions;
use Filament\Resources\Pages\EditRecord;

class EditBancoMovimientoSplit extends EditRecord
{
    protected static string $resource = BancoMovimientoSplitResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\ViewAction::make(),
            Actions\DeleteAction::make(),
        ];
    }

    protected function getRedirectUrl(): string	{
	    return $this->previousUrl ?? $this->getResource()::getUrl('index');
    }
}
