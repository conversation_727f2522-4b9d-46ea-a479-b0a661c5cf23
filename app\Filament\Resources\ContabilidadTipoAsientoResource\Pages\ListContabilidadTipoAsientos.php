<?php

namespace App\Filament\Resources\ContabilidadTipoAsientoResource\Pages;

use App\Filament\Resources\ContabilidadTipoAsientoResource;
use Filament\Actions;
use Filament\Resources\Pages\ListRecords;

class ListContabilidadTipoAsientos extends ListRecords
{
    protected static string $resource = ContabilidadTipoAsientoResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\CreateAction::make(),
        ];
    }
}
