<?php

namespace App\Filament\Resources\EmpleadoResource\Pages;

use App\Filament\Resources\EmpleadoResource;
use App\Filament\Resources\EmpleadoResource\Widgets\EmpleadoImportesChart;
use App\Filament\Resources\EmpleadoResource\Widgets\EmpleadoPieChart;
use App\Models\Empleado;
use Filament\Actions;
use Filament\Resources\Pages\ViewRecord;

class ViewEmpleado extends ViewRecord
{
    protected static string $resource = EmpleadoResource::class;

    public function getTitle(): string  {
        return $this->record->nombre;
    }

    protected function getHeaderActions(): array
    {
        return [
            Actions\EditAction::make(),
        ];
    }

    protected function getHeaderWidgets():array
    {
        return [
            EmpleadoImportesChart::make(['empleadoId' => $this->record->id]),
            EmpleadoPieChart::make(['empleadoId' => $this->record->id]),
        ];
    }
}
