<?php

namespace App\Filament\Resources\CentroCosteResource\Widgets;

use Filament\Support\RawJs;
use Illuminate\Support\Facades\DB;
use Leandrocfe\FilamentApexCharts\Widgets\ApexChartWidget;

class CentrosCosteFacturacionChart extends ApexChartWidget
{
    protected static ?string $chartId = 'centroscosteFacturacionChart';
    protected static ?string $heading = 'Facturación por centro de coste';
    protected static ?string $pollingInterval = '120s';

     protected function getOptions(): array
     {
        $empresaId = session()->get('empresa_id');
        $registros = DB::select(
            'select coalesce(ccp.nombre, cc.nombre, "No asignado") as centrocoste
            , sum(-x.importe) as importe
            from contabilidad_apuntes_extended x
            inner join contabilidad_cuentas c on x.contabilidad_cuenta_id = c.id
            left join centro_costes cc on x.centro_coste_id = cc.id
            left join centro_costes ccp on cc.centro_coste_id = ccp.id
            where c.masa in (\'Ingresos\')
            and x.empresa_id = ?
            group by 1
            order by 1',
            [$empresaId]
        );
        $labels = array_map(fn($row) => $row->centrocoste, $registros);
        $data = array_map(fn($row) => floatval($row->importe), $registros);
        //$data = array_map(fn($row) => ['x' => $row->centrocoste, 'y' => floatval($row->importe)], $registros);

        return [
            'chart' => [
                'type' => 'bar',
                'height' => 300,
                'toolbar' => [
                    'show' => true,  // Mostrar la barra de herramientas
                ],
            ],
            'plotOptions' => [
                'bar' => [
                    'borderRadius' => 3,
                    'horizontal' => true,
                ],
            ],
            'series' => [
                [
                    'name' => 'Facturado',
                    'data' => $data,
                ],
            ],
            'xaxis' => [
                'categories' => $labels,
                'labels' => [
                    'style' => [
                        'colors' => '#9ca3af',
                        'fontWeight' => 600,
                    ],
                ],
            ],
        ];
    }

    protected function extraJsOptions(): ?RawJs
    {
        return RawJs::make(<<<'JS'
        {
            xaxis: {
                labels: {
                    formatter: function (val, index) {
                        return new Intl.NumberFormat('es-ES', {
                            maximumFractionDigits: 0
                        }).format(val)
                    }
                }
            },
            dataLabels: {
                enabled: true,
                formatter: function (val, index) {
                        return new Intl.NumberFormat('es-ES', {
                            maximumFractionDigits: 0
                        }).format(val)
                    },
            }
        }
        JS);
    }


}
