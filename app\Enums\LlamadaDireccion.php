<?php
namespace App\Enums;

use Filament\Support\Contracts\HasLabel;
use Filament\Support\Contracts\HasIcon;

enum LlamadaDireccion: string implements HasLabel, HasIcon
{
    case Entrante = 'Entrante';
    case Saliente = 'Saliente';

    public function getLabel(): string
    {
        return $this->name;
    }

    public function getIcon(): ?string
    {
        return match ($this) {
            self::Entrante => 'heroicon-o-phone-arrow-down-left',
            self::Saliente => 'heroicon-o-phone-arrow-up-right',
        };
    }
}


