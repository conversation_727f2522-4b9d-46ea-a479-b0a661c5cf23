<?php

namespace App\Filament\Resources\ContabilidadAsientoResource\Pages;

use App\Filament\Resources\ContabilidadAsientoResource;
use Filament\Actions;
use Filament\Resources\Pages\ViewRecord;

class ViewContabilidadAsiento extends ViewRecord
{
    protected static string $resource = ContabilidadAsientoResource::class;

    public function getTitle(): string  {
        return 'Asiento: ' . $this->record->ejercicio . '/' . $this->record->asiento_num;
    }

    protected function getHeaderActions(): array
    {
        return [
            Actions\Action::make('ver_lista')
                ->label('Ver Asientos')
                ->icon('heroicon-s-arrow-left')
                ->color('secondary')
                ->url(fn () => url('/contabilidad-asientos')),
            Actions\EditAction::make(),
            ];
    }
}
