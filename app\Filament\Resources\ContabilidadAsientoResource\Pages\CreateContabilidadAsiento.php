<?php

namespace App\Filament\Resources\ContabilidadAsientoResource\Pages;

use App\Filament\Resources\ContabilidadAsientoResource;
use App\Models\ContabilidadAsiento;
use Filament\Actions;
use Filament\Resources\Pages\CreateRecord;

class CreateContabilidadAsiento extends CreateRecord
{
    protected static string $resource = ContabilidadAsientoResource::class;

    protected function mutateFormDataBeforeCreate(array $data): array
    {
        return ContabilidadAsiento::onCreate($data);
    }
}

