<?php

namespace App\Policies;

use App\Models\User;
use App\Models\Tarea;
use App\Models\Empleado;
use App\Traits\HasGranularPermissions;
use Illuminate\Auth\Access\HandlesAuthorization;

class TareaPolicy
{
    use HandlesAuthorization, HasGranularPermissions;

    /**
     * Determine whether the user can view any models.
     */
    public function viewAny(User $user): bool
    {
        $result = $this->canWithRestrictions($user, 'view_any_tarea', 'tarea');
        return $result['can'] || $user->role === 'admin';
    }

    /**
     * Determine whether the user can view the model.
     */
    public function view(User $user, Tarea $tarea): bool
    {
        $result = $this->canWithRestrictions($user, 'view_any_tarea', 'tarea');

        if (!$result['can']) {
            return false;
        }

        // Si no hay restricciones, puede ver todo
        if (!$result['restriction']) {
            return true;
        }

        $empleado = Empleado::where('user_id', $user->id)->first();
        if (!$empleado) {
            return false;
        }

        // Aplicar restricciones
        switch ($result['restriction']) {
            case 'only_own':
                return $tarea->a_empleado_id === $empleado->id || $tarea->created_by === $user->id;

            case 'only_team':
                // Verificar si es propio
                if ($tarea->a_empleado_id === $empleado->id || $tarea->created_by === $user->id) {
                    return true;
                }

                // Verificar si pertenece al equipo
                $equiposUsuario = \App\Models\EquipoUsuario::where('user_id', $user->id)->pluck('equipo_id');
                if ($equiposUsuario->isEmpty()) {
                    return false;
                }

                // Obtener usuarios del mismo equipo
                $usuariosDelEquipo = \App\Models\EquipoUsuario::whereIn('equipo_id', $equiposUsuario)->pluck('user_id');
                $empleadosDelEquipo = \App\Models\Empleado::whereIn('user_id', $usuariosDelEquipo)->pluck('id');

                return $empleadosDelEquipo->contains($tarea->a_empleado_id);

            default:
                return false;
        }
    }

    /**
     * Determine whether the user can create models.
     */
    public function create(User $user): bool
    {
        return $user->can('create_tarea');
    }

    /**
     * Determine whether the user can update the model.
     */
    public function update(User $user, Tarea $tarea): bool
    {
        return $user->can('update_tarea');
    }

    /**
     * Determine whether the user can delete the model.
     */
    public function delete(User $user, Tarea $tarea): bool
    {
        return $user->can('delete_tarea');
    }

    /**
     * Determine whether the user can bulk delete.
     */
    public function deleteAny(User $user): bool
    {
        return $user->can('delete_any_tarea');
    }

    /**
     * Determine whether the user can permanently delete.
     */
    public function forceDelete(User $user, Tarea $tarea): bool
    {
        return $user->can('force_delete_tarea');
    }

    /**
     * Determine whether the user can permanently bulk delete.
     */
    public function forceDeleteAny(User $user): bool
    {
        return $user->can('force_delete_any_tarea');
    }

    /**
     * Determine whether the user can restore.
     */
    public function restore(User $user, Tarea $tarea): bool
    {
        return $user->can('restore_tarea');
    }

    /**
     * Determine whether the user can bulk restore.
     */
    public function restoreAny(User $user): bool
    {
        return $user->can('restore_any_tarea');
    }

    /**
     * Determine whether the user can replicate.
     */
    public function replicate(User $user, Tarea $tarea): bool
    {
        return $user->can('replicate_tarea');
    }

    /**
     * Determine whether the user can reorder.
     */
    public function reorder(User $user): bool
    {
        return $user->can('reorder_tarea');
    }
}
