<?php

namespace App\Filament\Resources\ArticuloResource\RelationManagers;

use App\Models\Linea;
use Filament\Forms\Form;
use Filament\Resources\RelationManagers\RelationManager;
use Filament\Tables\Filters\Filter;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;

class LineaFacturaCompraRelationManager extends RelationManager
{
    protected static string $relationship = 'linea';
    protected static ?string $title = 'Facturas Compra';

    public function isReadOnly(): bool {
        return false;
    }

    public function form(Form $form): Form {
        return $form
            ->schema(Linea::getForm($this->getOwnerRecord()->id, 'App\\Models\\Factura'));
    }

    public function table(Table $table): Table
    {
        return Linea::configureSimpleTable($table, null, 'Factura', $this->getOwnerRecord()->id)
            ->filters([
                Filter::make('facturas compra')
                    ->query(fn (Builder $query):
                        Builder => $query
                            ->where('padre_type', 'App\\Models\\Factura')
                            ->whereHas('padre', function ($q) { $q->where('tipo', 'Compra'); })
                    )
                    ->default(),
            ])
            ->persistFiltersInSession();
    }

    public static function canViewForRecord(Model $ownerRecord, string $pageClass): bool { return getPermission(static::class, 'canView', static::$relationship); }
    public function canViewAny(): bool {return getPermission(get_class(), __FUNCTION__);}
    public function canEdit($record): bool {return getPermission(get_class(), __FUNCTION__);}
    public function canCreate(): bool {return getPermission(get_class(), __FUNCTION__);}
    public function canDelete($record): bool {return getPermission(get_class(), __FUNCTION__);}
    public function canDeleteAny(): bool {return getPermission(get_class(), __FUNCTION__);}
}
