<?php

namespace App\Filament\Resources\FicheroResource\Pages;

use App\Filament\Resources\FicheroResource;
use App\Models\Fichero;
use Filament\Actions;
use Filament\Resources\Pages\EditRecord;

class EditFichero extends EditRecord
{
    protected static string $resource = FicheroResource::class;

    protected function mutateFormDataBeforeSave(array $data): array
    {
        return Fichero::onCreate($data);
    }

    protected function getHeaderActions(): array
    {
        return [
            Actions\ViewAction::make(),
            Actions\DeleteAction::make(),
        ];
    }

    protected function getRedirectUrl(): string	{
	    return $this->previousUrl ?? $this->getResource()::getUrl('index');
    }
}
