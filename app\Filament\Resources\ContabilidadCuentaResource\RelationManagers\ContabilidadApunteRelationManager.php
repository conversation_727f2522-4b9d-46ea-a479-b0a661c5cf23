<?php

namespace App\Filament\Resources\ContabilidadCuentaResource\RelationManagers;

use App\Models\ContabilidadApunte;
use App\Models\ContabilidadAsiento;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\RelationManagers\RelationManager;
use Filament\Tables;
use Filament\Tables\Actions\BulkActionGroup;
use Filament\Tables\Actions\DeleteBulkAction;
use Filament\Tables\Filters\SelectFilter;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;
use Illuminate\Database\Eloquent\Model;

class ContabilidadApunteRelationManager extends RelationManager
{
    protected static string $relationship = 'ContabilidadApunte';
    protected static ?string $title = 'Apuntes';
    public function isReadOnly(): bool { return false; }


    public function form(Form $form): Form
    {
        traza(get_called_class(), __FUNCTION__,'id', $form->model?->id ?? 'nuevo');
        return $form
            ->schema(ContabilidadApunte::getForm(null, $this->getOwnerRecord()->id));
    }

    public function table(Table $table): Table
    {
        return $table
            ->striped()
            ->columns(ContabilidadApunte::getTabla(null, $this->getOwnerRecord()->id))
            ->modifyQueryUsing(function (Builder $query) {
                return $query->with([
                    'contabilidadAsiento:id,fecha,ejercicio,asiento_num,concepto,contabilidad_tipo_asiento_id',
                    'contabilidadAsiento.contabilidadTipoAsiento:id,nombre',
                    'contabilidadCuenta:id,cuenta_id,nombre',
                    'documento:id,num'
                ]);
            })
            ->defaultSort('contabilidadAsiento.fecha', 'desc')
            ->filters(ContabilidadApunte::getFilters())
            ->persistFiltersInSession()
            ->headerActions([
                Tables\Actions\CreateAction::make(),
            ])
            ->paginated([25, 50, 100, 'all'])
            ->actions([
                Tables\Actions\EditAction::make()
                    ->label(false)
                    ->tooltip('Editar'),
                Tables\Actions\DeleteAction::make()
                    ->label(false)
                    ->tooltip('Borrar'),
            ])
            ->bulkActions([
                BulkActionGroup::make([
                    DeleteBulkAction::make(),
                ]),
            ]);
    }

    public static function canViewForRecord(Model $ownerRecord, string $pageClass): bool { return getPermission(static::class, 'canView', static::$relationship); }
    public function canViewAny(): bool {return getPermission(get_class(), __FUNCTION__);}
    public function canEdit($record): bool {return getPermission(get_class(), __FUNCTION__);}
    public function canCreate(): bool {return getPermission(get_class(), __FUNCTION__);}
    public function canDelete($record): bool {return getPermission(get_class(), __FUNCTION__);}
    public function canDeleteAny(): bool {return getPermission(get_class(), __FUNCTION__);}
}
