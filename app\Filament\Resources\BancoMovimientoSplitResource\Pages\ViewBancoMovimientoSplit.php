<?php

namespace App\Filament\Resources\BancoMovimientoSplitResource\Pages;

use App\Filament\Resources\BancoMovimientoSplitResource;
use Filament\Actions;
use Filament\Resources\Pages\ViewRecord;

class ViewBancoMovimientoSplit extends ViewRecord
{
    protected static string $resource = BancoMovimientoSplitResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\EditAction::make()
                ->label(false)
                ->tooltip('Editar'),
        ];
    }
}
