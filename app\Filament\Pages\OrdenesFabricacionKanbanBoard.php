<?php

namespace App\Filament\Pages;

use App\Enums\OrdenFabricacionEstado;
use App\Models\OrdenFabricacion;
use App\Models\Proyecto;
use App\Models\Empleado;
use Carbon\Carbon;
use Mokhosh\FilamentKanban\Pages\KanbanBoard;
use Filament\Actions\CreateAction;
use Filament\Actions\Action;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\DatePicker;
use Illuminate\Support\Collection;


class OrdenesFabricacionKanbanBoard extends KanbanBoard
{
    protected static string $model = OrdenFabricacion::class;
    protected static string $statusEnum = OrdenFabricacionEstado::class;
    protected static ?string $title = 'Órdenes Fabricación Kanban';

    protected static string $headerView = 'ordenesfabricacion-kanban.kanban-header';
    protected static string $recordView = 'ordenesfabricacion-kanban.kanban-record';

    protected static string $recordTitleAttribute = 'nombre';
    protected static string $recordStatusAttribute = 'estado';

    protected string $editModalTitle = 'Editar OF';
    protected string $editModalSaveButtonLabel = 'Guardar';
    protected string $editModalCancelButtonLabel = 'Cancelar';
    protected string $editModalWidth = '2xl';

    // Propiedades para filtros
    public ?int $filtroProyecto = null;
    public ?int $filtroEmpleado = null;
    public ?string $filtroFechaDesde = null;
    public ?string $filtroFechaHasta = null;



    public static function getNavigationLabel(): string { return 'Kanban OFs'; }

    public static function getNavigationGroup(): ?string { return 'Proyectos'; }

    protected function records(): Collection
    {
        $query = OrdenFabricacion::query();

        // Filtro base: excluir terminadas excepto las de la última semana
        $query->where(function ($q) {
            $q->whereNotIn('estado', [OrdenFabricacionEstado::Terminada])
              ->orWhere(function ($q) {
                  $q->where('estado', OrdenFabricacionEstado::Terminada)
                    ->where('fecha_fin', '>=', Carbon::now()->subDays(7));
              });
        });

        // Aplicar filtros
        if ($this->filtroProyecto) {
            $query->where('proyecto_id', $this->filtroProyecto);
        }

        if ($this->filtroEmpleado) {
            $query->where('empleado_id', $this->filtroEmpleado);
        }

        if ($this->filtroFechaDesde) {
            $query->where('fecha', '>=', $this->filtroFechaDesde);
        }

        if ($this->filtroFechaHasta) {
            $query->where('fecha', '<=', $this->filtroFechaHasta);
        }

        return $query->get();
    }
   

    protected function getEditModalFormSchema(string|int|null $recordId): array
    {
        return OrdenFabricacion::getKanbanForm();
    }

    public function onStatusChanged(string|int $recordId, string $status, array $fromOrderedIds, array $toOrderedIds): void
    {
        $of = OrdenFabricacion::find($recordId);
        $of->cambiarEstado($status);
    }

    protected function getHeaderActions(): array
    {
        return [
            Action::make('filtros')
                ->label('Filtros')
                ->icon('heroicon-o-funnel')
                ->color('gray')
                ->form([
                    Select::make('filtroProyecto')
                        ->label('Proyecto')
                        ->options(Proyecto::all()->pluck('codigo_nombre', 'id'))
                        ->searchable()
                        ->placeholder('Todos los proyectos'),

                    Select::make('filtroEmpleado')
                        ->label('Empleado')
                        ->options(Empleado::all()->pluck('nombre', 'id'))
                        ->searchable()
                        ->placeholder('Todos los empleados'),

                    DatePicker::make('filtroFechaDesde')
                        ->label('Fecha desde'),

                    DatePicker::make('filtroFechaHasta')
                        ->label('Fecha hasta'),
                ])
                ->action(function (array $data): void {
                    $this->filtroProyecto = $data['filtroProyecto'];
                    $this->filtroEmpleado = $data['filtroEmpleado'];
                    $this->filtroFechaDesde = $data['filtroFechaDesde'];
                    $this->filtroFechaHasta = $data['filtroFechaHasta'];
                })
                ->fillForm([
                    'filtroProyecto' => $this->filtroProyecto,
                    'filtroEmpleado' => $this->filtroEmpleado,
                    'filtroFechaDesde' => $this->filtroFechaDesde,
                    'filtroFechaHasta' => $this->filtroFechaHasta,
                ]),

            Action::make('limpiarFiltros')
                ->label('Limpiar Filtros')
                ->icon('heroicon-o-x-mark')
                ->color('gray')
                ->action(function (): void {
                    $this->filtroProyecto = null;
                    $this->filtroEmpleado = null;
                    $this->filtroFechaDesde = null;
                    $this->filtroFechaHasta = null;
                })
                ->visible(fn (): bool =>
                    $this->filtroProyecto ||
                    $this->filtroEmpleado ||
                    $this->filtroFechaDesde ||
                    $this->filtroFechaHasta
                ),

            CreateAction::make()
                ->model(OrdenFabricacion::class)
                ->form(OrdenFabricacion::getKanbanForm())
        ];
    }






}
