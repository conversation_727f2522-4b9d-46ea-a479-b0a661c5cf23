<?php

namespace App\Filament\Resources\EquipoUsuarioResource\Pages;

use App\Filament\Resources\EquipoUsuarioResource;
use Filament\Actions;
use Filament\Resources\Pages\EditRecord;

class EditEquipoUsuario extends EditRecord
{
    protected static string $resource = EquipoUsuarioResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\ViewAction::make(),
            Actions\DeleteAction::make(),
        ];
    }

    protected function getRedirectUrl(): string	{
	    return $this->previousUrl ?? $this->getResource()::getUrl('index');
    }
}
