<?php

namespace App\Filament\Resources\ContabilidadAsignacionCuentaResResource\Pages;

use App\Filament\Resources\ContabilidadAsignacionCuentaResResource;
use Filament\Actions;
use Filament\Resources\Pages\ViewRecord;

class ViewContabilidadAsignacionCuentaRes extends ViewRecord
{
    protected static string $resource = ContabilidadAsignacionCuentaResResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\EditAction::make(),
        ];
    }
}
