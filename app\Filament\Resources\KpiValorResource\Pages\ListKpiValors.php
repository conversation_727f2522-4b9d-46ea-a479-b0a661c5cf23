<?php

namespace App\Filament\Resources\KpiValorResource\Pages;

use App\Filament\Resources\KpiValorResource;
use Filament\Actions;
use Filament\Resources\Pages\ListRecords;

class ListKpiValors extends ListRecords
{
    protected static string $resource = KpiValorResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\CreateAction::make(),
        ];
    }
}
