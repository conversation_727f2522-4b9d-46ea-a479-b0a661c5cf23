<?php

namespace App\Http\Resources\V1;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class OrdenFabricacionTrabajoApiResource extends JsonResource
{
    protected function datosComunes(): array
    {
        return [
            'orden_fabricacion_id' => $this->orden_fabricacion_id,
            'horas' => $this->horas,
            'tipo_hora' => $this->horaTipo->nombre,
            'descripcion' => $this->descripcion,
            'empresa_id' => $this->empresa_id,
        ];
    }

    public function toArray(Request $request): array
    {
         return [
            'type' => 'ordenFabricacionTrabajo',
            'id' => $this->id,
            'attributes' => $this->datosComunes(),
        ];
    }

    public function toCsvArray(Request $request): array
    {
        return array_merge([
            'id' => $this->id,
        ], $this->datosComunes());
    }
}
