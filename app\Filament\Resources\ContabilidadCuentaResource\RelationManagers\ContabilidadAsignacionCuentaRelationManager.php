<?php

namespace App\Filament\Resources\ContabilidadCuentaResource\RelationManagers;

use App\Models\ContabilidadAsignacionCuenta;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\RelationManagers\RelationManager;
use Filament\Tables;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;
use Illuminate\Database\Eloquent\Model;

class ContabilidadAsignacionCuentaRelationManager extends RelationManager
{
    protected static string $relationship = 'ContabilidadAsignacionCuenta';
    protected static ?string $title = 'Asig. centro coste';
    public function isReadOnly(): bool { return false; }

    public function form(Form $form): Form
    {
        return $form
            ->schema(ContabilidadAsignacionCuenta::getForm($this->getOwnerRecord()->cuenta_id));
    }

    public function table(Table $table): Table
    {
        return $table
            ->striped()
            ->columns([
                TextColumn::make('centroCoste.nombre')
                    ->sortable(),
                TextColumn::make('fecha_desde')
                    ->date('d-m-Y')
                    ->sortable(),
                TextColumn::make('fecha_hasta')
                    ->date('d-m-Y')
                    ->sortable(),
                TextColumn::make('porcentaje')
                    ->numeric()
                    ->sortable(),
            ])
            ->filters([
                //
            ])
            ->headerActions([
                Tables\Actions\CreateAction::make(),
            ])
            ->actions([
                Tables\Actions\EditAction::make()
                    ->label(false)
                    ->tooltip('Editar'),
                Tables\Actions\DeleteAction::make()
                    ->label(false)
                    ->tooltip('Borrar'),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }

    public static function canViewForRecord(Model $ownerRecord, string $pageClass): bool { return getPermission(static::class, 'canView', static::$relationship); }
    public function canViewAny(): bool {return getPermission(get_class(), __FUNCTION__);}
    public function canEdit($record): bool {return getPermission(get_class(), __FUNCTION__);}
    public function canCreate(): bool {return getPermission(get_class(), __FUNCTION__);}
    public function canDelete($record): bool {return getPermission(get_class(), __FUNCTION__);}
    public function canDeleteAny(): bool {return getPermission(get_class(), __FUNCTION__);}
}
