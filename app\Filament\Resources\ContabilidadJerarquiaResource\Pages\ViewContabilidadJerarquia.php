<?php

namespace App\Filament\Resources\ContabilidadJerarquiaResource\Pages;

use App\Filament\Resources\ContabilidadJerarquiaResource\Widgets\JerarquiaSaldosChart;
use App\Filament\Resources\ContabilidadJerarquiaResource;
use App\Filament\Resources\ContabilidadJerarquiaResource\Widgets\JerarquiaImportesChart;
use Filament\Actions;
use Filament\Resources\Pages\ViewRecord;

class ViewContabilidadJerarquia extends ViewRecord
{
    protected static string $resource = ContabilidadJerarquiaResource::class;

    public function getTitle(): string  {
        return $this->record->nombre;
    }

    protected function getHeaderActions(): array
    {
        return [
            Actions\Action::make('ver_lista')
                ->label('Ver Jerarquías')
                ->icon('heroicon-s-arrow-left')
                ->color('secondary')
                ->url(fn () => url('/contabilidad-jerarquias')),
            Actions\EditAction::make(),
        ];
    }

    protected function getHeaderWidgets():array {
        return [
            JerarquiaImportesChart::make(['jerarquiaId' => $this->record->id]),
            JerarquiaSaldosChart::make(['jerarquiaId' => $this->record->id]),
        ];
    }
}
