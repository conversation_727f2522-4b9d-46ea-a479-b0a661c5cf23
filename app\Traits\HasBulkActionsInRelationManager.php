<?php

namespace App\Traits;

use Filament\Tables\Actions\BulkActionGroup;
use Filament\Tables\Actions\DeleteBulkAction;

trait HasBulkActionsInRelationManager
{
    /**
     * IMPORTANTE: Habilita las bulk actions asegurando que el RelationManager no sea read-only
     * Sin esto, los checkboxes y bulk actions no aparecerán
     */
    public function isReadOnly(): bool 
    { 
        return false; 
    }

    /**
     * Configuración específica de bulk actions para RelationManager
     */
    protected function getBulkActions(): array
    {
        $actions = [];
        
        if ($this->canDeleteAny()) {
            $actions[] = DeleteBulkAction::make()
                ->requiresConfirmation()
                ->label('Eliminar seleccionados')
                ->modalHeading('Eliminar registros seleccionados')
                ->modalDescription('¿Estás seguro de que quieres eliminar los registros seleccionados? Esta acción no se puede deshacer.')
                ->modalSubmitActionLabel('Sí, eliminar')
                ->successNotificationTitle('Registros eliminados correctamente');
        }
        
        return $actions;
    }
    
    /**
     * Configuración adicional para la tabla que mejora las bulk actions
     */
    protected function configureBulkActionsTable($table)
    {
        return $table
            ->bulkActions($this->getBulkActions())
            ->checkIfRecordIsSelectableUsing(fn () => true)
            ->selectCurrentPageOnly(false);
    }
}
