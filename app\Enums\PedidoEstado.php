<?php
namespace App\Enums;

use Filament\Support\Contracts\HasColor;

enum PedidoEstado: string implements HasColor
{
    case Pendiente = 'Pendiente';
    case Aprobado = 'Aprobado';
    case Iniciado = 'Iniciado';
    case Cancelado = 'Cancelado';
    case En_Proceso = 'En Proceso';
    case Entregado = 'Entregado';

    public function getColor(): string
    {
        return match($this) {
            self::Pendiente => 'danger',
            self::Iniciado => 'primary',
            self::Aprobado => 'primary',
            self::Cancelado => 'danger',
            self::En_Proceso => 'primary',
            self::Entregado => 'success',
        };
    }
}


