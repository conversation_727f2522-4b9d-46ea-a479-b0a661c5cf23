<?php

namespace App\Filament\Resources\ContabilidadPresupuestoResource\Pages;

use App\Filament\Resources\ContabilidadPresupuestoResource;
use Filament\Actions;
use Filament\Resources\Pages\EditRecord;

class EditContabilidadPresupuesto extends EditRecord
{
    protected static string $resource = ContabilidadPresupuestoResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\ViewAction::make(),
            Actions\DeleteAction::make(),
        ];
    }

    protected function getRedirectUrl(): string	{
	    return $this->previousUrl ?? $this->getResource()::getUrl('index');
    }
}
