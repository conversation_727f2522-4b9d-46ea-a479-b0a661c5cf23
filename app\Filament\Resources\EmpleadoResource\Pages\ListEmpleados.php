<?php

namespace App\Filament\Resources\EmpleadoResource\Pages;

use App\Filament\Resources\EmpleadoResource;
use Filament\Actions;
use Filament\Resources\Components\Tab;
use Filament\Resources\Pages\ListRecords;

class ListEmpleados extends ListRecords
{
    protected static string $resource = EmpleadoResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\CreateAction::make(),
        ];
    }

    Public function getTabs(): array
    {
        return [
            'todos' => Tab::make('Todos'),
            'activos' => Tab::make('Activos')
                ->modifyQueryUsing(function ($query) {
                    return $query->where('estado', 'activo');
                }),
        ];
    }
}
