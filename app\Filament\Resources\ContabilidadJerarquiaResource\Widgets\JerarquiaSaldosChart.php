<?php

namespace App\Filament\Resources\ContabilidadJerarquiaResource\Widgets;

use Filament\Support\RawJs;
use Illuminate\Support\Facades\DB;
use Leandrocfe\FilamentApexCharts\Widgets\ApexChartWidget;

class JerarquiaSaldosChart extends ApexChartWidget
{
    protected static ?string $chartId = 'jerarquiaSaldosChart';
    protected static ?string $heading = 'Saldos';
    protected static ?string $pollingInterval = '120s';

    public $jerarquiaId;

    /**
     * Chart options (series, labels, types, size, animations...)
     * https://apexcharts.com/docs/options
     *
     * @return array
     */

     protected function getOptions(): array {
        $registros = DB::select(
            'SELECT fecha, SUM(-s.saldo) as valor
            FROM contabilidad_saldos s
             JOIN contabilidad_cuentas c on s.contabilidad_cuenta_id = c.id
             join contabilidad_jerarquias j1 on (c.contabilidad_jerarquia_id = j1.id or c.pgc_jerarquia_id = j1.id)
             left join contabilidad_jerarquias j2 on j1.contabilidad_jerarquia_id = j2.id
             left join contabilidad_jerarquias j3 on j2.contabilidad_jerarquia_id = j3.id
             WHERE(j1.id = ? or j2.id = ? or j3.id = ?) 
             GROUP BY 1
             ORDER BY 1',
            [$this->jerarquiaId, $this->jerarquiaId, $this->jerarquiaId]
        );

        $labels = array_map(fn($row) => $row->fecha, $registros);
        $data = array_map(fn($row) => $row->valor, $registros);

          return [
             'chart' => [
                 'type' => 'area',
                 'height' => 300,
                 'stacked' => true,
             ],
             'series' => [
                 [
                     'name' => 'Saldo',
                     'data' =>  $data,
                 ],
             ],
             'xaxis' => [
                 'categories' =>  $labels,
                 'type' => 'datetime',
                 'labels' => [
                     'style' => [
                         'fontFamily' => 'inherit',
                     ],
                 ],
             ],
             'yaxis' => [
                 'labels' => [
                     'style' => [
                         'fontFamily' => 'inherit',
                     ],
                 ],
            ],
            'colors' => ['#f59e0b', '#FF5733'],
            'plotOptions' => [
                'bar' => [
                    'borderRadius' => 3,
                    'horizontal' => false,
                ],
            ],
        ];
    }

    protected function extraJsOptions(): ?RawJs
    {
        return RawJs::make(<<<'JS'
        {
            yaxis: {
                labels: {
                    formatter: function (val, index) {
                        return new Intl.NumberFormat('es-ES', {
                            maximumFractionDigits: 0
                        }).format(val)
                    }
                }
            },
            dataLabels: {
                enabled: false,
            }
        }
        JS);
    }
}
