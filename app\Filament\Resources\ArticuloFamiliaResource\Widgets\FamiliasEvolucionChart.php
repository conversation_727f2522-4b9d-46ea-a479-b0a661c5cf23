<?php

namespace App\Filament\Resources\ArticuloFamiliaResource\Widgets;

use Filament\Support\RawJs;
use Illuminate\Support\Facades\DB;
use Leandrocfe\FilamentApexCharts\Widgets\ApexChartWidget;

class FamiliasEvolucionChart extends ApexChartWidget
{
    /**
     * Chart Id
     *
     * @var string
     */
    protected static ?string $chartId = 'familiasEvolucionChart';

    /**
     * Widget Title
     *
     * @var string|null
     */
    protected static ?string $heading = 'Facturación por familia';

    /**
     * Chart options (series, labels, types, size, animations...)
     * https://apexcharts.com/docs/options
     *
     * @return array
     */

     protected function getOptions(): array
     {
        $empresaId = session()->get('empresa_id');
        $registros = DB::select(
            'SELECT DATE_FORMAT(f.fecha, \'%Y\') as fecha
            , ifnull(af.familia, \'Otros\') as familia
	        , sum(l.importe) as importe
            from lineas l
	        inner join facturas f on l.padre_id = f.id
	        left join articulos a on l.articulo_id = a.id
	        left join articulo_familias af on a.articulo_familia_id = af.id
	        where l.empresa_id = ? and l.padre_type =\'App\\\\Models\\\\Factura\' and f.tipo = "Venta"
	        group by 1,2
            ORDER BY 1',
            [$empresaId]
        );
        $data = [];
        foreach ($registros as $registro) {
            $data[$registro->fecha][$registro->familia] = $registro->importe;
        }

        $fechas = array_keys($data);
        $familias = [];
        foreach ($data as $familias_data) {
            $familias = array_unique(array_merge($familias, array_keys($familias_data)));
        }

        $series = [];
        foreach ($familias as $familia) {
            $serie_data = [];
            foreach ($fechas as $fecha) {
                $serie_data[] = $data[$fecha][$familia] ?? 0;
            }
            $series[] = [
                'name' => $familia,
                'data' => $serie_data,
            ];
        }

        return [
            'chart' => [
                'type' => 'bar',
                'height' => 300,
                'stacked' => true,
            ],
            'series' => $series,
            'xaxis' => [
                'categories' =>  $fechas,
                'labels' => [
                    'style' => [
                        'fontFamily' => 'inherit',
                    ],
                ],
            ],
            'yaxis' => [
                'title' => [
                    'text' => 'Facturado',
                ],
            ],
            'plotOptions' => [
                'bar' => [
                    'borderRadius' => 3,
                    'horizontal' => false,
                ],
            ],
        ];
    }

    protected function extraJsOptions(): ?RawJs
    {
        return RawJs::make(<<<'JS'
        {
            yaxis: {
                labels: {
                    formatter: function (val, index) {
                        return new Intl.NumberFormat('es-ES', {
                            maximumFractionDigits: 0
                        }).format(val)
                    }
                }
            },
            dataLabels: {
                enabled: false,
            }
        }
        JS);
    }
}
