<?php

namespace App\Filament\Resources\FacturaResource\Pages;

use App\Filament\Resources\FacturaResource;
use Filament\Actions;
use Filament\Resources\Pages\ListRecords;
use Filament\Resources\Components\Tab;

class ListFacturas extends ListRecords
{
    protected static string $resource = FacturaResource::class;

    Public function getTabs(): array
    {
        return [
            'Venta' => Tab::make('Venta')
                ->modifyQueryUsing(function ($query) {
                    return $query->where('tipo', 'Venta');
                }),
            'Compra' => Tab::make('Compra')
                ->modifyQueryUsing(function ($query) {
                    return $query->where('tipo', 'Compra');
                }),
            'pedientesv' => Tab::make('Venta Pendientes')
                ->modifyQueryUsing(function ($query) {
                    return $query->whereIn('estado', ['Pendiente', 'Vencida'])
                        ->where('tipo', 'Venta');
                }),
            'pedientesc' => Tab::make('Compra Pendientes')
                ->modifyQueryUsing(function ($query) {
                    return $query->where('estado', 'Pendiente')
                        ->where('tipo', 'Compra');
                }),
            'vencidas' => Tab::make('Venta Vencidas')
                ->modifyQueryUsing(function ($query) {
                    return $query->where('estado', 'Vencida');
                }),
        ];
    }


    protected function getHeaderActions(): array
    {
        return [
            Actions\CreateAction::make(),
        ];
    }
}
