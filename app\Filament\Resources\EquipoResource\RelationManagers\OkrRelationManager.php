<?php

namespace App\Filament\Resources\EquipoResource\RelationManagers;

use App\Models\Okr;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\RelationManagers\RelationManager;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;
use Illuminate\Database\Eloquent\Model;

class OkrRelationManager extends RelationManager
{
    protected static string $relationship = 'Okr';
    protected static ?string $title = 'OKRs';

    public function isReadOnly(): bool   {
        return false;
    }

    public function form(Form $form): Form
    {
        return $form
            ->schema(Okr::getForm(null, null, $this->getOwnerRecord()->id));
    }

    public function table(Table $table): Table
    {
        return $table
            ->striped()
            ->columns(Okr::getTabla(null, null, $this->getOwnerRecord()->id))
            ->filters([
                //
            ])
            ->headerActions([
                Tables\Actions\CreateAction::make(),
            ])
            ->actions([
                Tables\Actions\EditAction::make()
					->label(false)
					->tooltip('Editar'),
				Tables\Actions\DeleteAction::make()
					->label(false)
					->tooltip('Borrar'),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }

    public static function canViewForRecord(Model $ownerRecord, string $pageClass): bool { return getPermission(static::class, 'canView', static::$relationship); }
    public function canViewAny(): bool {return getPermission(get_class(), __FUNCTION__);}
    public function canEdit($record): bool {return getPermission(get_class(), __FUNCTION__);}
    public function canCreate(): bool {return getPermission(get_class(), __FUNCTION__);}
    public function canDelete($record): bool {return getPermission(get_class(), __FUNCTION__);}
    public function canDeleteAny(): bool {return getPermission(get_class(), __FUNCTION__);}
}
