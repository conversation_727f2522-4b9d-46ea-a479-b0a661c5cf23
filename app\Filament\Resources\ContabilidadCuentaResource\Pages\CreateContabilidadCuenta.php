<?php

namespace App\Filament\Resources\ContabilidadCuentaResource\Pages;

use App\Filament\Resources\ContabilidadCuentaResource;
use App\Models\ContabilidadCuenta;
use Filament\Actions;
use Filament\Resources\Pages\CreateRecord;

class CreateContabilidadCuenta extends CreateRecord
{
    protected static string $resource = ContabilidadCuentaResource::class;

    protected function mutateFormDataBeforeCreate(array $data): array
    {
        return ContabilidadCuenta::onCreate($data);
    }
}
