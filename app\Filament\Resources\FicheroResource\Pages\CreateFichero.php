<?php

namespace App\Filament\Resources\FicheroResource\Pages;

use App\Filament\Resources\FicheroResource;
use App\Models\Fichero;
use Filament\Actions;
use Filament\Resources\Pages\CreateRecord;

class CreateFichero extends CreateRecord
{
    protected static string $resource = FicheroResource::class;

    protected function mutateFormDataBeforeCreate(array $data): array
    {
        return Fichero::onCreate($data);
    }

    protected function getRedirectUrl(): string
    {
        return $this->getResource()::getUrl('index');
    }

}
