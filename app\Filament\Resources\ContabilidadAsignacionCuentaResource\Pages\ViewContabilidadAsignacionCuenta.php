<?php

namespace App\Filament\Resources\ContabilidadAsignacionCuentaResource\Pages;

use App\Filament\Resources\ContabilidadAsignacionCuentaResource;
use Filament\Actions;
use Filament\Resources\Pages\ViewRecord;

class ViewContabilidadAsignacionCuenta extends ViewRecord
{
    protected static string $resource = ContabilidadAsignacionCuentaResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\Action::make('ver_lista')
                ->label('Ver Asignaciones')
                ->icon('heroicon-s-arrow-left')
                ->color('secondary')
                ->url(fn () => url('/contabilidad-asignacion-cuentas')),
            Actions\EditAction::make(),
        ];
    }
}
