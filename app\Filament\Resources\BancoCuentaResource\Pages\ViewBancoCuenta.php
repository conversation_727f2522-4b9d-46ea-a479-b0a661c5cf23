<?php

namespace App\Filament\Resources\BancoCuentaResource\Pages;

use App\Filament\Resources\BancoCuentaResource;
use Filament\Actions;
use Filament\Resources\Pages\ViewRecord;

class ViewBancoCuenta extends ViewRecord
{
    protected static string $resource = BancoCuentaResource::class;

    public function getTitle(): string  {
        return $this->record->nombre;
    }

    protected function getHeaderActions(): array
    {
        return [
            Actions\Action::make('ver_lista')
                ->label('Ver Cuentas bancarias')
                ->icon('heroicon-s-arrow-left')
                ->color('secondary')
                ->url(fn () => url('/banco-cuentas')),
            Actions\EditAction::make()
                ->label(false)
                ->tooltip('Editar'),
        ];
    }
}
