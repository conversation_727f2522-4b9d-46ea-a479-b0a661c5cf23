<?php

namespace App\Filament\Resources\ContabilidadApunteExtendedResource\Pages;

use App\Filament\Resources\ContabilidadApunteExtendedResource;
use Filament\Actions;
use Filament\Resources\Pages\EditRecord;

class EditContabilidadApunteExtended extends EditRecord
{
    protected static string $resource = ContabilidadApunteExtendedResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\ViewAction::make(),
            Actions\DeleteAction::make(),
        ];
    }

    protected function getRedirectUrl(): string	{
	    return $this->previousUrl ?? $this->getResource()::getUrl('index');
    }
}
