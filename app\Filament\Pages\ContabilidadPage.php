<?php

namespace App\Filament\Pages;

use Filament\Pages\Page;
use Filament\Widgets\Widget;
use Illuminate\Support\Facades\DB;
use Filament\Forms\Contracts\HasForms;
use Filament\Forms\Form;
use Filament\Forms\Components\Textarea;
use Filament\Actions\Action;
use Illuminate\Support\Facades\Http;

class ContabilidadPage extends Page implements HasForms
{
    protected static ?string $navigationIcon = 'heroicon-o-chat-bubble-left-right';
    protected static string $view = 'filament.pages.contabilidad-page';

    public string $question = '';
    public ?string $answer = null;

    public function form(Form $form): Form
    {
        return $form
            ->schema([
                Textarea::make('question')
                    ->label('Escribe tu pregunta')
                    ->required(),
            ])
            ->statePath('data');
    }

    public function askLlama(): void
    {
        $this->validate([
            'question' => 'required|string',
        ]);

        try {
            $response = Http::post('http://127.0.0.1:11434/api/generate', [
                'model' => 'llama3.2:latest',
                'prompt' => $this->question,
                'stream' => false,
            ]);

            if ($response->successful()) {
                $this->answer = $response->json('response');
            } else {
                $this->answer = 'Error al comunicarse con Llama.';
            }
        } catch (\Exception $e) {
            $this->answer = 'Error: ' . $e->getMessage();
        }
    }

    protected function getHeaderActions(): array
    {
        return [
            Action::make('Enviar Pregunta')
                ->action('askLlama')
                ->requiresConfirmation()
                ->label('Preguntar a Llama'),
        ];
    }
}



