<?php

namespace App\Filament\Resources\FicheroResource\Pages;

use App\Filament\Resources\FicheroResource;
use Filament\Actions;
use Filament\Resources\Pages\ListRecords;
use Filament\Resources\Components\Tab;

class ListFicheros extends ListRecords
{
    protected static string $resource = FicheroResource::class;

    Public function getTabs(): array
    {
        return [
            'todos' => Tab::make('Todos'),
            'pendientes' => Tab::make('Pendientes')
                ->modifyQueryUsing(function ($query) {
                    return $query->where('estado', 'Pendiente');
                }),
            'errores' => Tab::make('Errores')
                ->modifyQueryUsing(function ($query) {
                    return $query->where('estado', 'Error');
                }),
            'procesados' => Tab::make('Procesados')
                ->modifyQueryUsing(function ($query) {
                    return $query->where('estado', 'Procesado');
                }),
        ];
    }

    protected function getHeaderActions(): array
    {
        return [
            Actions\CreateAction::make(),
        ];
    }
}
