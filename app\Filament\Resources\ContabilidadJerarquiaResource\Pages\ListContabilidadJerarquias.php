<?php

namespace App\Filament\Resources\ContabilidadJerarquiaResource\Pages;

use App\Filament\Resources\ContabilidadJerarquiaResource;
use Filament\Actions;
use Filament\Resources\Pages\ListRecords;
use Filament\Resources\Components\Tab;

class ListContabilidadJerarquias extends ListRecords
{
    protected static string $resource = ContabilidadJerarquiaResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\CreateAction::make(),
        ];
    }

    Public function getTabs(): array
    {
        return [
            'todos' => Tab::make('Todas'),
            'pgc' => Tab::make('PGC')
                ->modifyQueryUsing(function ($query) {
                    return $query->where('raiz', 'PGC');
                }),
            'ctares' => Tab::make('Cuenta de Res.')
                ->modifyQueryUsing(function ($query) {
                    return $query->where('raiz', 'Cuenta de resultados');
                }),
            'balance' => Tab::make('Balance')
                ->modifyQueryUsing(function ($query) {
                    return $query->where('raiz', 'Balance');
                }),
        ];
    }
}
