<?php

namespace App\Http\Resources\V1;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class OrdenFabricacionEntradaApiResource extends JsonResource
{
    protected function datosComunes(): array
    {
        return [
            'orden_fabricacion_id' => $this->orden_fabricacion_id,
            'articulo_id' => $this->articulo_id,
            'cantidad' => $this->cantidad,
            'descripcion' => $this->descripcion,
            'linea_id' => $this->linea_id,
            'solicitud_compra_linea_id' => $this->solicitud_compra_linea_id,
            'empresa_id' => $this->empresa_id,
        ];
    }

    public function toArray(Request $request): array
    {
         return [
            'type' => 'ordenFabricacionEntrada',
            'id' => $this->id,
            'attributes' => $this->datosComunes(),
        ];
    }

    public function toCsvArray(Request $request): array
    {
        return array_merge([
            'id' => $this->id,
        ], $this->datosComunes());
    }
}
