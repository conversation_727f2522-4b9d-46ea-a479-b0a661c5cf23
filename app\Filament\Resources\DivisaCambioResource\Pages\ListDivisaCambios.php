<?php

namespace App\Filament\Resources\DivisaCambioResource\Pages;

use App\Filament\Resources\DivisaCambioResource;
use Filament\Actions;
use Filament\Resources\Pages\ListRecords;

class ListDivisaCambios extends ListRecords
{
    protected static string $resource = DivisaCambioResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\CreateAction::make(),
        ];
    }
}
