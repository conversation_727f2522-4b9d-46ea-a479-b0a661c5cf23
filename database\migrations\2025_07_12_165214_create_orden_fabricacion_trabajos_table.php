<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('ordenes_fabricacion_trabajos', function (Blueprint $table) {
            $table->id();
            $table->foreignId('empresa_id')->constrained()->onUpdate('cascade')->onDelete('cascade')->index('ix_oftr_empr');
            $table->foreignId('orden_fabricacion_id')->constrained('ordenes_fabricacion')->onUpdate('cascade')->onDelete('cascade');
            $table->decimal('horas', 9, 3);
            $table->foreignId('hora_tipo_id')->constrained()->onUpdate('cascade');
            $table->string('descripcion', 200)->nullable();
            $table->foreignId('created_by')->nullable()->constrained('users')->onDelete('set null');
            $table->foreignId('updated_by')->nullable()->constrained('users')->onDelete('set null');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('ordenes_fabricacion_trabajos');
    }
};
