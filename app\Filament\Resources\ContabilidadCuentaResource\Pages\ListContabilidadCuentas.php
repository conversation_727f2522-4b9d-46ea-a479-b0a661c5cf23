<?php

namespace App\Filament\Resources\ContabilidadCuentaResource\Pages;

use App\Filament\Resources\ContabilidadCuentaResource;
use Filament\Actions;
use Filament\Resources\Pages\ListRecords;
use Filament\Resources\Components\Tab;

class ListContabilidadCuentas extends ListRecords
{
    protected static string $resource = ContabilidadCuentaResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\CreateAction::make(),
        ];
    }

    Public function getTabs(): array
    {
        return [
            'todos' => Tab::make('Todas las cuentas'),
            'activo' => Tab::make('Activo')
                ->modifyQueryUsing(function ($query) {
                    return $query->where('masa', 'Activo');
                }),
            'pasivo' => Tab::make('Pasivo')
                ->modifyQueryUsing(function ($query) {
                    return $query->where('masa', 'Pasivo');
                }),
            'gastos' => Tab::make('Gastos')
                ->modifyQueryUsing(function ($query) {
                    return $query->where('masa', 'Gastos');
                }),
            'ingresos' => Tab::make('Ingresos')
                ->modifyQueryUsing(function ($query) {
                    return $query->where('masa', 'Ingresos');
                }),
        ];
    }
}
