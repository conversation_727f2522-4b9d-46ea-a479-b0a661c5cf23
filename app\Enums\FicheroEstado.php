<?php
namespace App\Enums;

use Filament\Support\Contracts\HasColor;

enum FicheroEstado: string implements HasColor
{
    case Pendiente = 'Pendiente';
    case Error = 'Error';
    case Procesado = 'Procesado';


    public function getColor(): string|array|null
    {
        return match($this) {
            self::Pendiente => 'primary',
            self::Error => 'danger',
            self::Procesado => 'success',
        };
    }
}


