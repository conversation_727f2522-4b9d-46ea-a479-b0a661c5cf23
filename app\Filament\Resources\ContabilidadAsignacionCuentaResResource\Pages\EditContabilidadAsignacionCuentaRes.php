<?php

namespace App\Filament\Resources\ContabilidadAsignacionCuentaResResource\Pages;

use App\Filament\Resources\ContabilidadAsignacionCuentaResResource;
use Filament\Actions;
use Filament\Resources\Pages\EditRecord;

class EditContabilidadAsignacionCuentaRes extends EditRecord
{
    protected static string $resource = ContabilidadAsignacionCuentaResResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\ViewAction::make(),
            Actions\DeleteAction::make(),
        ];
    }

    protected function getRedirectUrl(): string	{
	    return $this->previousUrl ?? $this->getResource()::getUrl('index');
    }
}
