<?php

namespace App\Policies;

use App\Models\User;
use App\Models\KpiValor;
use Illuminate\Auth\Access\HandlesAuthorization;

class KpiValorPolicy
{
    use HandlesAuthorization;

    /**
     * Determine whether the user can view any models.
     */
    public function viewAny(User $user): bool
    {
        return $user->can('view_any_kpi::valor');
    }

    /**
     * Determine whether the user can view the model.
     */
    public function view(User $user, KpiValor $kpiValor): bool
    {
        return $user->can('view_kpi::valor');
    }

    /**
     * Determine whether the user can create models.
     */
    public function create(User $user): bool
    {
        return $user->can('create_kpi::valor');
    }

    /**
     * Determine whether the user can update the model.
     */
    public function update(User $user, KpiValor $kpiValor): bool
    {
        return $user->can('update_kpi::valor');
    }

    /**
     * Determine whether the user can delete the model.
     */
    public function delete(User $user, KpiValor $kpiValor): bool
    {
        return $user->can('delete_kpi::valor');
    }

    /**
     * Determine whether the user can bulk delete.
     */
    public function deleteAny(User $user): bool
    {
        return $user->can('delete_any_kpi::valor');
    }

    /**
     * Determine whether the user can permanently delete.
     */
    public function forceDelete(User $user, KpiValor $kpiValor): bool
    {
        return $user->can('force_delete_kpi::valor');
    }

    /**
     * Determine whether the user can permanently bulk delete.
     */
    public function forceDeleteAny(User $user): bool
    {
        return $user->can('force_delete_any_kpi::valor');
    }

    /**
     * Determine whether the user can restore.
     */
    public function restore(User $user, KpiValor $kpiValor): bool
    {
        return $user->can('restore_kpi::valor');
    }

    /**
     * Determine whether the user can bulk restore.
     */
    public function restoreAny(User $user): bool
    {
        return $user->can('restore_any_kpi::valor');
    }

    /**
     * Determine whether the user can replicate.
     */
    public function replicate(User $user, KpiValor $kpiValor): bool
    {
        return $user->can('replicate_kpi::valor');
    }

    /**
     * Determine whether the user can reorder.
     */
    public function reorder(User $user): bool
    {
        return $user->can('reorder_kpi::valor');
    }
}
