<?php

namespace App\Filament\Resources\FacturaResource\RelationManagers;

use App\Models\Hora;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\RelationManagers\RelationManager;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\SoftDeletingScope;
use Illuminate\Database\Eloquent\Model;

class HoraRelationManager extends RelationManager
{
    protected static string $relationship = 'Hora';
    protected static ?string $title = 'Horas proyecto';

    public function isReadOnly(): bool { return false; }

    public function form(Form $form): Form
    {
        return $form
            ->schema(Hora::getForm(null, null, $this->getOwnerRecord()->id));
    }

    public function table(Table $table): Table
    {
        return $table
            ->striped()
            ->columns(Hora::getTabla($this->getOwnerRecord()->id))
            ->defaultSort('inicio', 'desc')
            ->filters(Hora::getFilters())
            ->persistSortInSession()
            ->persistSearchInSession()
            ->persistColumnSearchesInSession()
            ->headerActions([
                Tables\Actions\CreateAction::make(),
            ])
            ->actions([
                Tables\Actions\EditAction::make()
					->label(false)
					->tooltip('Editar'),
				Tables\Actions\DeleteAction::make()
					->label(false)
					->tooltip('Borrar'),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }

    public static function canViewForRecord(Model $ownerRecord, string $pageClass): bool { return getPermission(static::class, 'canView', static::$relationship); }
    public function canViewAny(): bool {return getPermission(get_class(), __FUNCTION__);}
    public function canEdit($record): bool {return getPermission(get_class(), __FUNCTION__);}
    public function canCreate(): bool {return getPermission(get_class(), __FUNCTION__);}
    public function canDelete($record): bool {return getPermission(get_class(), __FUNCTION__);}
    public function canDeleteAny(): bool {return getPermission(get_class(), __FUNCTION__);}
}
