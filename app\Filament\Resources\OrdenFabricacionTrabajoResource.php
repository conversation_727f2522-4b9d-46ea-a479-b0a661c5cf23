<?php

namespace App\Filament\Resources;

use App\Filament\Resources\OrdenFabricacionTrabajoResource\Pages;
use App\Models\OrdenFabricacionTrabajo;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;

class OrdenFabricacionTrabajoResource extends Resource
{
    protected static ?string $model = OrdenFabricacionTrabajo::class;
    protected static ?string $navigationGroup = "Proyectos";
    public static function getLabel(): string { return 'OF trabajo'; }
    public static function getPluralLabel(): string  {return 'OF trabajos';}

    public static function form(Form $form): Form
    {
        traza(get_called_class(), __FUNCTION__,'id', $form->model?->id ?? 'nuevo');
        return $form
            ->schema(OrdenFabricacionTrabajo::getForm());
    }

    public static function table(Table $table): Table
    {
        traza(get_called_class(), __FUNCTION__);
        return $table
            ->columns(OrdenFabricacionTrabajo::getTabla())
            ->filters([
                //
            ])
            ->actions([
                Tables\Actions\ViewAction::make()
                    ->label(false)
                    ->tooltip('Ver'),
                Tables\Actions\EditAction::make()
                    ->label(false)
                    ->tooltip('Editar'),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListOrdenFabricacionTrabajos::route('/'),
            'create' => Pages\CreateOrdenFabricacionTrabajo::route('/create'),
            'view' => Pages\ViewOrdenFabricacionTrabajo::route('/{record}'),
            'edit' => Pages\EditOrdenFabricacionTrabajo::route('/{record}/edit'),
        ];
    }

    public static function canViewAny(): bool {return getPermission(get_class(), __FUNCTION__);}
    public static function canView($record): bool {return getPermission(get_class(), __FUNCTION__);}
    public static function canEdit($record): bool {return getPermission(get_class(), __FUNCTION__);}
    public static function canCreate(): bool {return getPermission(get_class(), __FUNCTION__);}
    public static function canDelete($record): bool {return getPermission(get_class(), __FUNCTION__);}
    public static function canDeleteAny(): bool {return getPermission(get_class(), __FUNCTION__);}

}
