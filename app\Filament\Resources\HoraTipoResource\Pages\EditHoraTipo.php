<?php

namespace App\Filament\Resources\HoraTipoResource\Pages;

use App\Filament\Resources\HoraTipoResource;
use Filament\Actions;
use Filament\Resources\Pages\EditRecord;

class EditHoraTipo extends EditRecord
{
    protected static string $resource = HoraTipoResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\ViewAction::make(),
            Actions\DeleteAction::make(),
        ];
    }

    protected function getRedirectUrl(): string	{
	    return $this->previousUrl ?? $this->getResource()::getUrl('index');
    }
}
