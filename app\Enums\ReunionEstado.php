<?php
namespace App\Enums;

use Filament\Support\Contracts\HasColor;
use Filament\Support\Contracts\HasLabel;
use Filament\Support\Contracts\HasIcon;

enum ReunionEstado: string implements HasColor, HasLabel, HasIcon
{
    case Planificada = 'Planificada';
    case No_Realizada = 'No Realizada';
    case Realizada = 'Realizada';


    public function getColor(): string|array|null
    {
        return match($this) {
            self::Planificada => 'gray',
            self::No_Realizada => 'danger',
            self::Realizada => 'success',
        };
    }

    public function getLabel(): string
    {
        return match ($this) {
            self::Planificada => 'Planificada',
            self::No_Realizada => 'No Realizada',
            self::Realizada => 'Realizada',
        };
    }

    public function getIcon(): ?string
    {
        return match ($this) {
            self::Planificada => 'heroicon-o-calendar-days',
            self::No_Realizada => 'heroicon-o-x-circle',
            self::Realizada => 'heroicon-o-check-circle',
        };
    }

}


