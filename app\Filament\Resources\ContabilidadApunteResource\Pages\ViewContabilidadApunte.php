<?php

namespace App\Filament\Resources\ContabilidadApunteResource\Pages;

use App\Filament\Resources\ContabilidadApunteResource;
use Filament\Actions;
use Filament\Resources\Pages\ViewRecord;

class ViewContabilidadApunte extends ViewRecord
{
    protected static string $resource = ContabilidadApunteResource::class;

    public function getTitle(): string  {
        return 'Apunte: ' . $this->record->contabilidadAsiento->ejercicio . '/' . $this->record->contabilidadAsiento->asiento_num . '.' . $this->record->apunte_num;
    }

    protected function getHeaderActions(): array
    {
        return [
            Actions\Action::make('ver_lista')
                ->label('Ver Apuntes')
                ->icon('heroicon-s-arrow-left')
                ->color('secondary')
                ->url(fn () => url('/contabilidad-apuntes')),
            Actions\EditAction::make(),
        ];
    }
}
