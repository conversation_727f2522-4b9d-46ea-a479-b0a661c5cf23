<?php
namespace App\Enums;

use Filament\Support\Contracts\HasColor;
use Filament\Support\Contracts\HasLabel;
use Filament\Support\Contracts\HasIcon;
use Mokhosh\FilamentKanban\Concerns\IsKanbanStatus;

enum OrdenFabricacionEstado: string implements HasColor, HasLabel, HasIcon
{
    use IsKanbanStatus;
    case Planificada = 'Planificada';
    case Lanzada = 'Lanzada';
    case Activa = 'Activa';
    case Terminada = 'Terminada';


    public function getColor(): string|array|null
    {
        return match($this) {
            self::Planificada => 'gray',
            self::Lanzada => 'primary',
            self::Activa => 'danger',
            self::Terminada => 'success',
        };
    }

    public function getLabel(): string
    {
        return match ($this) {
            self::Planificada => 'Planificada',
            self::Lanzada => 'Lanzada',
            self::Activa => 'Activa',
            self::Terminada => 'Terminada',
        };
    }

    public function getIcon(): ?string
    {
        return match ($this) {
            self::Planificada => 'heroicon-o-calendar-days',
            self::Lanzada => 'heroicon-o-clock',
            self::Activa => 'heroicon-o-play',
            self::Terminada => 'heroicon-o-check',
        };
    }

    public static function kanbanCases(): array
    {
        return [
            static::Planificada,
            static::Lanzada,
            static::Activa,
            static::Terminada,
        ];
    }

}


