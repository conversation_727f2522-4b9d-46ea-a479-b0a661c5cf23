<?php

use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Auth;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Components\Select;

function camelCaseToPermission(string $input): string {
    return strtolower(preg_replace('/(?<!^)([A-Z])/', '::\1', $input));
}

if(! function_exists('getPermission')) {
    function getPermission($clase, $funcion, $relationship = null) : bool
    {
        $user = auth()->user();

        if ($relationship) {
                $clase = $relationship;
        } else {        
            $partes = explode('\\', $clase);
            $clase = end($partes); // Última parte
            $clase = str_replace('Resource', '', $clase);
            $clase = str_replace('RelationManager', '', $clase);
        }
        $clase = camelCaseToPermission($clase);
        //dump($clase);

        $funcion = str_replace("can", "", $funcion);
        $funcion = str_replace("authorize", "", $funcion);
        $funcion = str_replace("Edit", "update", $funcion);

        $primercar = $funcion[0];
        $funcion = substr($funcion,1);
        $funcion = strtolower($primercar . preg_replace('/(?=[A-Z])/', '_', $funcion));

        $permissionName = $funcion . '_' . $clase;

        //dump("Usuario: " . auth()->user()->email . " - Permiso evaluado: " . $permissionName);

        $salida = $user->can( $permissionName);
        $salida = $salida || ($user->role == 'admin');
        return $salida;
    }
}

if (! function_exists('soloAlfanumericos')) {
    function soloAlfanumericos($cadena) {
        // Eliminar cualquier carácter que no sea número o letra
        $cadena = preg_replace('/[^a-zA-Z0-9]/', '', $cadena);
        return strtolower($cadena);
    }
}

if (! function_exists('detectarSeparador')) {
    function detectarSeparador($filePath) {
        $handle  = fopen($filePath, "r");
        if (!$handle ) {
            return false; // No se pudo abrir el archivo
        }
        $firstLine = fgets($handle );
        $comas = substr_count($firstLine, ',');
        $puntoYComa = substr_count($firstLine, ';');
        fclose($handle );
        return $puntoYComa > $comas ? ';' : ',';
    }
}

if (! function_exists('formatearFecha')) {
    function formatearFecha($fecha) {
        if (empty($fecha)) {
            return null; // Si el valor está vacío, devolver null
        }

        $formatos = [
            'd/m/Y', // 29/10/2021
            'd-m-Y', // 29-10-2021
            'Y/m/d', // 2021/10/29
            'Y-m-d', // 2021-10-29
            'Ymd',   // 20211029
            'dmY',   // 29102021
            'm/d/Y', // 10/29/2021 (formato estadounidense)
            'm-d-Y', // 10-29-2021
        ];

        // Probar cada formato y devolver el formateo correcto
        foreach ($formatos as $formato) {
            $fecha_obj = DateTime::createFromFormat($formato, $fecha);
            if ($fecha_obj && $fecha_obj->format($formato) === $fecha) {
                return $fecha_obj->format('Y-m-d'); // Formato MySQL
            }
        }
        // Si no coincide con ningún formato, devolver null
        return null;
    }
}

if (! function_exists('convertirValor')) {
    function convertirValor($valor, $tipo) {
        if (empty($valor) && ($valor <> "0")) {
            return null;  // Si el valor está vacío, devolver null
        } elseif ($tipo === 'string') {
            return trim(mb_convert_encoding($valor, 'UTF-8', 'Windows-1252'));  // Eliminar espacios en blanco y devolver la cadena
        } elseif ($tipo === 'integer') {
            return is_numeric($valor) ? (int)$valor : null;  // Convertir a entero si es numérico
        } elseif ($tipo === 'number') {
            $val = formatearNumero($valor);
            return is_numeric($val) ? $val : null;
        } elseif ($tipo === 'date') {
            return formatearFecha($valor);  // Usar la función de formateo de fechas que creamos antes
        }

        return null;  // Si el tipo no es soportado, devolver null
    }
}

if (!function_exists('formatearNumero')) {
    function formatearNumero($str) {
        // Elimina espacios en blanco
        $str = trim($str);

        // Si tiene una coma y un punto, detectamos el formato
        if (strpos($str, ',') !== false && strpos($str, '.') !== false) {
            // Si el punto está antes de la coma, el punto es separador de miles y la coma es separador decimal
            if (strpos($str, '.') < strpos($str, ',')) {
                $str = str_replace('.', '', $str); // Elimina puntos como separadores de miles
                $str = str_replace(',', '.', $str); // Reemplaza la coma por punto como separador decimal
            } else {
                // Si el punto está después de la coma, la coma es separador de miles y el punto es separador decimal
                $str = str_replace(',', '', $str); // Elimina comas como separadores de miles
            }
        } elseif (strpos($str, ',') !== false) {
            // Si solo hay comas, la coma es separador decimal
            $str = str_replace(',', '.', $str); // Reemplaza la coma por punto como separador decimal
        }
        return $str;
    }
}

if (!function_exists('str_replace_array')) {
    function str_replace_array($search, array $replace, $subject)
    {
        foreach ($replace as $value) {
            $subject = preg_replace('/' . preg_quote($search, '/') . '/', is_numeric($value) ? $value : "'$value'", $subject, 1);
        }
        return $subject;
    }
}

if (! function_exists('extractNumberSQL')){
    function extractNumberSQL(string $linea, int $posicion, int $longitud) : float
    {
        $salida = trim(mb_substr($linea, $posicion, $longitud));
        $salida = str_replace('.', '', $salida); //quita millares
        $salida = str_replace(',', '.', $salida); //formatea decimales a punto
        return (float) $salida;
    }
}

if (! function_exists('calcularIBAN')){
    function calcularIBAN($entidad, $oficina, $dc, $cuenta) {
        $resultado = preg_match('/^\d{4}$/', $entidad) &&
            preg_match('/^\d{4}$/', $oficina) &&
            preg_match('/^\d{2}$/', $dc) &&
            preg_match('/^\d{10}$/', $cuenta);
        if (!$resultado) {
            return null;
        }

        $bban = $entidad . $oficina . $dc . $cuenta;
        // Añadir el código de país ('ES') y convertirlo a números (E = 14, S = 28)
        $bban_con_pais = $bban . '142800';  // ES se convierte en 1428 y se añade al final
        // Calcular el módulo 97 del BBAN con el código del país añadido
        $mod97 = intval(bcmod($bban_con_pais, '97'));
        // Calcular los dos dígitos de control del IBAN
        $dc_iban = str_pad(98 - $mod97, 2, '0', STR_PAD_LEFT);
        $iban = 'ES' . $dc_iban . $bban;
        return $iban;
    }
}

if (! function_exists('calcularDC')){
    function calcularDC($entidad, $oficina, $cuenta) {
        $pesos = [1, 2, 4, 8, 5, 10, 9, 7, 3, 6];

        $resultado = preg_match('/^\d{4}$/', $entidad) &&
            preg_match('/^\d{4}$/', $oficina) &&
            preg_match('/^\d{10}$/', $cuenta);
        if (!$resultado) {
            return null;
        }

        $entidad_oficina = str_split($entidad . $oficina);
        $suma_dc1 = 0;
        for ($i = 0; $i < 8; $i++) {
            $suma_dc1 += $entidad_oficina[$i] * $pesos[$i+2];  // Se usan los primeros 8 pesos
        }
        $dc1 = 11 - ($suma_dc1 % 11);
        if ($dc1 == 10) {
            $dc1 = 1;
        } elseif ($dc1 == 11) {
            $dc1 = 0;
        }

        // Convertir la cuenta a array de dígitos
        $cuenta_digitos = str_split($cuenta);  // 10 dígitos de la cuenta

        // Cálculo del segundo dígito de control (DC2) para la cuenta
        $suma_dc2 = 0;
        for ($i = 0; $i < 10; $i++) {
            $suma_dc2 += $cuenta_digitos[$i] * $pesos[$i];  // Se usan los 10 pesos completos
        }
        $dc2 = 11 - ($suma_dc2 % 11);
        if ($dc2 == 10) {
            $dc2 = 1;
        } elseif ($dc2 == 11) {
            $dc2 = 0;
        }

        // Devolver los dos dígitos de control concatenados
        return (string)$dc1 . (string)$dc2;
    }
}

if (! function_exists('esCSV')){
    function esCSV($filePath, $expected_fields, &$mapped_columns, &$mapped_indices, &$mapped_types, $required_fields, &$separador) {
        traza(null,__FUNCTION__, null, $filePath);

        $separador = detectarSeparador($filePath);
        if (!$separador){
            return false;
        }

        $handle  = fopen($filePath, "r");
        $header = fgetcsv($handle , 1023, $separador);
        fclose($handle );

        foreach ($header as $index => $column) {
            $column = soloAlfanumericos($column);
            foreach ($expected_fields as $standard => $data) {
                if (in_array($column, $data['aliases'])) {
                    $mapped_columns[] = $standard;
                    $mapped_indices[] = $index;
                    $mapped_types[] = $data['type'];
                    break;
                }
            }
        }

        //valida si están todos los campos obligatorios
        $missing_fields = array_diff($required_fields, $mapped_columns);
        return empty($missing_fields);
    }
}


if (! function_exists('cargaCSV')){
    function cargaCSV( $modelo, string $fichero, &$mapped_columns, &$mapped_indices, &$mapped_types, $separador): string {
        traza(null, __FUNCTION__, null, $fichero);
        try {
            $handle = fopen($fichero, "r");
            //$handle = mb_convert_encoding($handle, 'UTF-8', 'ISO-8859-1');
            $empresaId = session()->get('empresa_id');

            $data = fgetcsv($handle, 1023, $separador);
            while (($data = fgetcsv($handle, 1023, $separador)) !== FALSE) {
                unset($registro);
                $registro = [
                    'empresa_id' => $empresaId,
                    'created_at' => now(),
                    'updated_at' => now(),
                ];
                foreach ($mapped_indices as $i => $index) {
                    $column = $mapped_columns[$i];
                    $tipo = $mapped_types[$i];
                    $valor = convertirValor($data[$index], $tipo);
                    $registro[$column] = $valor;
                }
                $modelo::insert($registro);
            }
            fclose($handle);

            return true;
        } catch (Exception $e) {
            traza(null, __FUNCTION__, 'Error', $e->getMessage());
            fclose($handle);
            //abort(500, "Error al insertar el registro: " . $e->getMessage());
            return false;
        }
    }
}

if (! function_exists('compareHeaders')){
    function compareHeaders(array $headers, array $expectedHeaders) {
        return count(array_intersect($headers, $expectedHeaders)) === count($expectedHeaders);
    }
}

if (! function_exists('normalizeHeader')){
    function normalizeHeader($header)
    {
        $header = strtolower(trim($header)); // Convertir a minúsculas y eliminar espacios
        $header = str_replace(['á', 'é', 'í', 'ó', 'ú', 'ü', 'ñ'], ['a', 'e', 'i', 'o', 'u', 'u', 'n'], $header); // Eliminar tildes y caracteres especiales
        $header = preg_replace('/[^a-z0-9]/', '_', $header); // Sustituir caracteres no alfanuméricos por "_"
        $header = preg_replace('/_+/', '_', $header); // Eliminar guiones bajos consecutivos
        return $header;
    }
}

if (! function_exists('traza')){
    function traza($clase="", $funcion="", $titulo="", $adicional="") {
        $empresaId = session()->get('empresa_id', '0'); // Empresa por defecto "0" si no está en sesión
        $userId = Auth::user()?->id ?? 'guest'; // Si no hay usuario autenticado, se registra como "guest"
        $ip = request()->ip(); // Obtener la IP del usuario

        $identificacion = "E{$empresaId}U{$userId}|{$ip} ";

        // Reemplazos de rutas para hacerlas más cortas
        $clase = str_replace('App\\Filament\\Resources', 'AFR', $clase);
        $clase = str_replace('App\\Http\\Controllers', 'AHC', $clase);

        if ($adicional>"") {
            $adicional = ': ' . $adicional;
        }

        // Obtener la URL base desde el .env
        $baseUrl = rtrim(config('app.url'), '/'); // Elimina la barra final si existe
        $clase = str_replace($baseUrl . '/api/', '', urldecode($clase));

        if($titulo=='Error'){
            Log::error($identificacion . "\t". $clase . "\t" . $funcion . "\t" . $titulo. $adicional);
        } else {
            Log::info(' ' . $identificacion . "\t". $clase . "\t" . $funcion . "\t" . $titulo. $adicional);
        }
    }
}

if (! function_exists('getCamposDinamicos')){
    function getCamposDinamicos(array|null $definicion, array|string|null $valores = null, string $nombreCampo = 'atributos'): array
    {
        $schema = [];

        if (is_string($valores)) {
            $valores = json_decode($valores, true) ?? [];
        }

        // Si $valores es array y tiene índice 0 con array, "desenvolver" ese primer índice
        if (is_array($valores) && isset($valores[0]) && is_array($valores[0])) {
            $valores = $valores[0];
        }

        // Si tiene la forma ["data" => [ ... ]], extraer el primero (por si acaso)
        if (is_array($valores) && isset($valores['data']) && is_array($valores['data'])) {
            $valores = $valores['data'][0] ?? [];
        }

        foreach ($definicion ?? [] as $campo) {
            $tipo = $campo['tipo'] ?? 'text';
            $nombre = $campo['nombre'] ?? null;
            $etiqueta = $campo['etiqueta'] ?? ucfirst($nombre);
            $opciones = $campo['opciones'] ?? [];

            // Si opciones es string, convertirlo a array asociativo para Filament
            if (is_string($opciones)) {
                $arrayOpciones = array_map('trim', explode(',', $opciones));
                $opciones = [];
                foreach ($arrayOpciones as $opt) {
                    $opciones[$opt] = $opt;
                }
            }

            $valor = $valores[$nombre] ?? null;

            if (! $nombre) {
                continue;
            }

            match ($tipo) {
                'select' => $schema[] = \Filament\Forms\Components\Select::make("{$nombreCampo}.{$nombre}")
                    ->label($etiqueta)
                    ->options($opciones),
                'number'=> $schema[] = TextInput::make("{$nombreCampo}.{$nombre}")
                    ->label($etiqueta)
                    ->numeric()
                    ->nullable(),
                default => $schema[] = TextInput::make("{$nombreCampo}.{$nombre}")
                    ->label($etiqueta),
            };
        }
        return $schema;

    }
}

if (!function_exists('wrapCampoEnData')) {
    function wrapCampoEnData(array $data, string $campo): array
    {
        if (!isset($data[$campo])) {
            return $data;
        }

        // Si ya está en formato ['data' => [ ... ]], no hacemos nada
        if (is_array($data[$campo]) &&
            isset($data[$campo]['data']) &&
            is_array($data[$campo]['data'])) {
            return $data;
        }

        // Si está en formato plano (array asociativo), lo envolvemos
        if (is_array($data[$campo])) {
            $data[$campo] = [
                'data' => [$data[$campo]]
            ];
        }

        // Si es un string JSON válido, intentamos decodificarlo
        if (is_string($data[$campo])) {
            $decoded = json_decode($data[$campo], true);
            if (is_array($decoded)) {
                $data[$campo] = [
                    'data' => [$decoded]
                ];
            }
        }

        return $data;
    }
}

if (!function_exists('unwrapCampoEnData')) {
    function unwrapCampoData(array $data, string $campo): array
    {
        if (isset($data[$campo])) {
            $json = $data[$campo];

            if (is_string($json)) {
                $decoded = json_decode($json, true);
            } else {
                $decoded = $json;
            }

            if (is_array($decoded) && isset($decoded['data'][0])) {
                $data[$campo] = $decoded['data'][0];
            }
        }
        return $data;
    }
}
