<?php

namespace App\Filament\Resources\ContabilidadApunteExtendedResource\Pages;

use App\Filament\Resources\ContabilidadApunteExtendedResource;
use Filament\Actions;
use Filament\Resources\Pages\ViewRecord;

class ViewContabilidadApunteExtended extends ViewRecord
{
    protected static string $resource = ContabilidadApunteExtendedResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\EditAction::make(),
        ];
    }
}
