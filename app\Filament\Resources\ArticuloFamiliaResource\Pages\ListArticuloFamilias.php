<?php

namespace App\Filament\Resources\ArticuloFamiliaResource\Pages;

use App\Filament\Resources\ArticuloFamiliaResource;
use App\Filament\Resources\ArticuloFamiliaResource\Widgets\FamiliasEvolucionChart;
use App\Filament\Resources\ArticuloFamiliaResource\Widgets\FamiliasPieChart;
use Filament\Actions;
use Filament\Resources\Pages\ListRecords;

class ListArticuloFamilias extends ListRecords
{
    protected static string $resource = ArticuloFamiliaResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\CreateAction::make(),
        ];
    }

    protected function getHeaderWidgets():array
    {
        return [
            FamiliasEvolucionChart::class,
            FamiliasPieChart::class,
        ];
    }
}
