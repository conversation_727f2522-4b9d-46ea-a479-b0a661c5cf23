<?php

namespace App\Filament\Resources\ContabilidadAsientoResource\Pages;

use App\Filament\Resources\ContabilidadAsientoResource;
use BezhanSalleh\FilamentShield\Traits\HasPageShield;
use Filament\Actions;
use Filament\Facades\Filament;
use Filament\Resources\Pages\ListRecords;

class ListContabilidadAsientos extends ListRecords
{

    protected static string $resource = ContabilidadAsientoResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\CreateAction::make(),
        ];
    }
}
