<?php

namespace App\Filament\Resources\BancoMovimientoResource\Pages;

use App\Filament\Resources\BancoMovimientoResource;
use Filament\Actions;
use Filament\Resources\Pages\EditRecord;

class EditBancoMovimiento extends EditRecord
{
    protected static string $resource = BancoMovimientoResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\ViewAction::make(),
            Actions\DeleteAction::make(),
        ];
    }

    protected function getRedirectUrl(): string	{
	    return $this->previousUrl ?? $this->getResource()::getUrl('index');
    }
}
