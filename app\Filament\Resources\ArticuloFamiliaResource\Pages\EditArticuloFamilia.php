<?php

namespace App\Filament\Resources\ArticuloFamiliaResource\Pages;

use App\Filament\Resources\ArticuloFamiliaResource;
use Filament\Actions;
use Filament\Resources\Pages\EditRecord;

class EditArticuloFamilia extends EditRecord
{
    protected static string $resource = ArticuloFamiliaResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\ViewAction::make(),
            Actions\DeleteAction::make(),
        ];
    }

    protected function getRedirectUrl(): string
	{
	    return $this->previousUrl ?? $this->getResource()::getUrl('index');
    }
}
