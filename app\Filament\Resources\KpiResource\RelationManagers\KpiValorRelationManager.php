<?php

namespace App\Filament\Resources\KpiResource\RelationManagers;

use App\Models\KpiValor;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\RelationManagers\RelationManager;
use Filament\Tables;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;
use Illuminate\Database\Eloquent\Model;

class KpiValorRelationManager extends RelationManager
{
    protected static string $relationship = 'KpiValor';
    protected static ?string $title = 'Valores';

    public function isReadOnly(): bool { return true; }

    public function form(Form $form): Form {
        return $form
            ->schema(KpiValor::getForm($this->getOwnerRecord()->id));
    }

    public function table(Table $table): Table {
        return $table
            ->striped()
            ->columns(KpiValor::getTabla($this->getOwnerRecord()->id))
            ->persistFiltersInSession()
            ->defaultSort('fecha', 'desc')
            ->filters(KpiValor::getFilters())
            ->headerActions([
                Tables\Actions\CreateAction::make(),
            ])
            ->actions([
                //Tables\Actions\EditAction::make(),
                //Tables\Actions\DeleteAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }

    public static function canViewForRecord(Model $ownerRecord, string $pageClass): bool { return getPermission(static::class, 'canView', static::$relationship); }
    public function canViewAny(): bool {return getPermission(get_class(), __FUNCTION__);}
    public function canEdit($record): bool {return getPermission(get_class(), __FUNCTION__);}
    public function canCreate(): bool {return getPermission(get_class(), __FUNCTION__);}
    public function canDelete($record): bool {return getPermission(get_class(), __FUNCTION__);}
    public function canDeleteAny(): bool {return getPermission(get_class(), __FUNCTION__);}
}
