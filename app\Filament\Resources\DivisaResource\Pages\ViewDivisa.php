<?php

namespace App\Filament\Resources\DivisaResource\Pages;

use App\Filament\Resources\DivisaResource;
use Filament\Actions;
use Filament\Resources\Pages\ViewRecord;

class ViewDivisa extends ViewRecord
{
    protected static string $resource = DivisaResource::class;

    public function getTitle(): string  {
        return $this->record->nombre;
    }

    protected function getHeaderActions(): array
    {
        return [
            Actions\EditAction::make(),
        ];
    }
}
